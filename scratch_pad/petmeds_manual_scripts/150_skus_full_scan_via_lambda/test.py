import os
import json
from browserbase import Browserbase


def generate_chicos_taxonomy():
    """
    Crawls chicos.com using BrowserBase to extract the product taxonomy.
    """
    print("--- Starting <PERSON>'s taxonomy crawl ---")

    try:
        # Initialize BrowserBase from environment variables
        browserbase = Browserbase()
    except Exception as e:
        print(
            f"Error: Could not initialize BrowserBase. Make sure BROWSERBASE_API_KEY and BROWSERBASE_PROJECT_ID are set.")
        print(e)
        return

    # These are top-level categories we want to ignore as they are not standard product types.
    CATEGORY_BLACKLIST = ['NEW', 'COLLECTIONS', 'SALE']

    # Define the nested selectors to extract the entire navigation hierarchy.
    # This structure mirrors the HTML structure of the Chico's mega-menu.
    selectors = {
        # 1. Start by finding each top-level list item in the main navigation bar.
        "query": "nav[aria-label='Main Menu'] > ul > li.nav-item--primary",
        "type": "list",
        "fields": {
            # 2. For each top-level item, get its name.
            "level1_name": {
                "query": "a.nav-link--primary > span",
                "type": "string"
            },
            # 3. And then find all the Level 2 items within it.
            "level2_items": {
                "query": "ul.nav-list--secondary > li.nav-item--secondary",
                "type": "list",
                "fields": {
                    # 4. For each Level 2 item, get its name.
                    "level2_name": {
                        "query": "a.nav-link--secondary",
                        "type": "string"
                    },
                    # 5. And then find all the Level 3 items within it.
                    "level3_items": {
                        "query": "ul.nav-list--tertiary > li.nav-item--tertiary",
                        "type": "list",
                        "fields": {
                            # 6. For each Level 3 item, get its name.
                            "level3_name": {
                                "query": "a.nav-link--tertiary",
                                "type": "string"
                            }
                        }
                    }
                }
            }
        }
    }

    print("Sending request to BrowserBase...")
    try:
        # Load the page and extract data with the defined selectors
        results = browserbase.load("https://www.chicos.com", selectors=selectors)

        # The result is a dictionary, the data we want is under the 'data' key
        extracted_data = results['data']
        print("Successfully extracted data from BrowserBase.")

    except Exception as e:
        print(f"An error occurred during the BrowserBase request: {e}")
        return

    # Process the structured JSON data into the desired " > " format
    print("\n--- Generating Taxonomy ---")
    taxonomy_lines = []
    for l1_item in extracted_data:
        l1_name = l1_item.get("level1_name", "").strip().upper()

        if not l1_name or l1_name in CATEGORY_BLACKLIST:
            continue

        level2_items = l1_item.get("level2_items", [])

        # Handle cases where a top-level category has no sub-items
        if not level2_items:
            taxonomy_lines.append(l1_name)
            continue

        for l2_item in level2_items:
            l2_name = l2_item.get("level2_name", "").strip()

            if not l2_name:
                continue

            level3_items = l2_item.get("level3_items", [])

            # Handle cases where a Level 2 category has no sub-items
            if not level3_items:
                taxonomy_lines.append(f"{l1_name} > {l2_name}")
                continue

            for l3_item in level3_items:
                l3_name = l3_item.get("level3_name", "").strip()
                if l3_name:
                    taxonomy_lines.append(f"{l1_name} > {l2_name} > {l3_name}")

    print("\n--- Chico's Product Taxonomy ---")
    for line in sorted(taxonomy_lines):
        print(line)

    # Optionally, save to a file
    with open("chicos_taxonomy.txt", "w") as f:
        for line in sorted(taxonomy_lines):
            f.write(line + "\n")
    print("\n--- Taxonomy saved to chicos_taxonomy.txt ---")


if __name__ == "__main__":
    generate_chicos_taxonomy()