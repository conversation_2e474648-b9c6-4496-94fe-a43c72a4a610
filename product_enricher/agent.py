from google.adk import Agent
from google.adk.models.lite_llm import LiteLlm

from product_enricher.image_attribute_extractor.tools.image_attribute_extractor_tool import extract_attributes
from product_enricher.product_seo_updator.tools.product_seo_updater_tool import \
    update_product

root_agent = Agent(
    name="product_enricher_agent",
    model=LiteLlm(model="openai/gpt-4o"),
    description=(
        "You are a coordinator for a product enricher agent. "
    ),
    instruction=(
        """
        You are the main agent who will coordinate with multiple available tools to extract attributes from product images.
        The following tools will be used for:
        extract_attributes: extract attributes from product images
        update_product: Update the product details based on the product and SEO keywords.
        
        Also make sure that you return the responses as provided by the tools
        """
    ),
    tools=[extract_attributes,update_product],
)

