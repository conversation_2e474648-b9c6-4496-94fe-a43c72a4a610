from typing import Optional

from openai import OpenAI

from product_enricher.image_attribute_extractor.prompts.ecommerce_image_prompt import prompt_template


def extract_attributes(images: list[str], metadata: Optional[dict] = None) ->str:
    """
    The following tool is used to extract attributes from product images using GPT-4.
    It takes a list of image URLs as input and returns a JSON object containing the extracted attributes.
    It also considers the optional metadata provided by the user.
    the format is as follows as below :
    {
        "title": "Product Title",
        "description": "Product Description",
        "attributes": [
            {attribute_name: "Color", attribute_value: "Beige", reason: "Visual Observation", source: "Image-1"},
            {attribute_name: "Material", attribute_value: "Acrylic", reason: "Visual Observation", source: "Image-1"},
        ],
        "seo keywords": ["Keyword 1", "Keyword 2", "Keyword 3"],
    }

    images: list[str] Images URLs of the product
    metadata: dict Optional metadata like name, description, category etc
    """
    client = OpenAI()
    metadata_string = ""
    if metadata:
        metadata_string = "\n".join(f" {key}: {value}" for key, value in metadata.items())

    content = [{"type": "input_text", "text": prompt_template + metadata_string}]
    for image in images:
        content.append(
        {
            "type": "input_image",
            "image_url": image,
        })

    response = client.responses.create(
        model="gpt-4.1",
        input=[{
            "role": "user",
            "content": content,
        }],
    )
    return response.output_text
