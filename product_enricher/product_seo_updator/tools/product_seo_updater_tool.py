from openai import OpenAI

from product_enricher.product_seo_updator.prompt.product_update_seo_input import prompt_seo_update

def update_product(product_details: str, seo_keywords: list[str]):
    """
    The following tool is responsible for updating the product details based on the provided seo keywords using GPT-4.
    product_details: product details as stringified json object
    seo_keywords: list of strings representing seo keywords
    """
    client = OpenAI()
    prompt = prompt_seo_update + f""" product details: {product_details} seo keywords: {seo_keywords}"""
    response = client.responses.create(
        model="gpt-4.1",
        input=[{
            "role": "user",
            "content": [
                {"type": "input_text", "text": prompt},
            ],
        }],
    )
    return response.output_text
