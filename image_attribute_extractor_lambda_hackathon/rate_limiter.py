import boto3
import datetime
from urllib.parse import urlparse

dynamodb = boto3.client('dynamodb')
TABLE_NAME = 'HostRateLimits'
DEFAULT_RATE_LIMIT = 100

"""
Assumes the following table structure of DDB - Table Name : HostRateLimits

| **hostDate (PK)**        | **count** | **ttl**       | **rateLimit** |
| ------------------------ | --------- | ------------- | ------------- |
| `example.com#2025-06-23` | `34`      | `<timestamp>` | *(none)*      | -> Current day's usage
| `example.com#config`     | *(none)*  | *(none)*      | `200`         | -> this is the default rate limit for example.com.

"""

def extract_host(url: str) -> str:
    return urlparse(url).netloc

def get_today_key(host: str) -> str:
    today = datetime.datetime.utcnow().strftime('%Y-%m-%d')
    return f"{host}#{today}"

def get_midnight_utc_ttl() -> int:
    now = datetime.datetime.utcnow()
    midnight = datetime.datetime.combine(now.date() + datetime.timedelta(days=1), datetime.time.min)
    return int(midnight.timestamp())

def get_rate_limit_for_host(host: str) -> int:
    config_key = f"{host}#config"

    try:
        response = dynamodb.get_item(
            TableName=TABLE_NAME,
            Key={'hostDate': {'S': config_key}},
            ProjectionExpression='rateLimit'
        )

        if 'Item' in response and 'rateLimit' in response['Item']:
            return int(response['Item']['rateLimit']['N'])

        # If config is missing, insert with default rate limit
        dynamodb.put_item(
            TableName=TABLE_NAME,
            Item={
                'hostDate': {'S': config_key},
                'rateLimit': {'N': str(DEFAULT_RATE_LIMIT)}
            }
        )
        return DEFAULT_RATE_LIMIT

    except Exception as e:
        # In case of failure, fallback to default but log exception
        print(f"[WARN] Failed to fetch or create rate limit for {host}: {e}")
        return DEFAULT_RATE_LIMIT

def get_current_count(host: str) -> int:
    key = get_today_key(host)
    try:
        response = dynamodb.get_item(
            TableName=TABLE_NAME,
            Key={'hostDate': {'S': key}},
            ProjectionExpression='count'
        )
        return int(response['Item']['count']['N']) if 'Item' in response and 'count' in response['Item'] else 0
    except Exception:
        return 0

def is_rate_limited(host: str) -> (bool, int, int):
    count = get_current_count(host)
    limit = get_rate_limit_for_host(host)
    return count >= limit, count, limit

def increment_count(host: str):
    key = get_today_key(host)
    ttl = get_midnight_utc_ttl()
    try:
        dynamodb.update_item(
            TableName=TABLE_NAME,
            Key={'hostDate': {'S': key}},
            UpdateExpression='SET #count = if_not_exists(#count, :zero) + :inc, #ttl = if_not_exists(#ttl, :ttl)',
            ExpressionAttributeNames={
                '#count': 'count',
                '#ttl': 'ttl'
            },
            ExpressionAttributeValues={
                ':inc': {'N': '1'},
                ':zero': {'N': '0'},
                ':ttl': {'N': str(ttl)}
            }
        )
    except Exception as e:
        raise RuntimeError(f"Failed to increment counter: {str(e)}")



