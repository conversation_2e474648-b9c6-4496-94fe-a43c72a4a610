import json
import logging

from image_attribute_extractor_lambda_hackathon.bff_bckup import handler as bff_dom_handler
from image_attribute_extractor_lambda_hackathon.bff import handler as bff_html_handler
from image_attribute_extractor_lambda_hackathon.extractor import handler as legacy_extractor_handler
from image_attribute_extractor_lambda_hackathon.product_extractor import handler as product_extractor_handler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

"""
This is the main source code for the commerce next. Dont change anything in this package unless confirmed with the team.
"""

def handler(event, context):
    try:
        body = json.loads(event.get("body", "{}"))
        logger.info(body)

        requestType = body.get("requestType")

        if requestType and requestType.lower() == "bff":
            return bff_dom_handler(event, context)
        if requestType and requestType.lower() == "bff_html":
            return bff_html_handler(event, context)
        if requestType and requestType.lower() == "product_extractor":
            return product_extractor_handler(event, context)
        else:
            return legacy_extractor_handler(event, context)

    except Exception as e:
        logger.error(f"Failed to handle the request: {e}")
        print(f"Error: {e}")


##############################################
########  commands to run this code ##########
### One time set up
### Copy credentials for aws and export them into your session

## One time login into ECR.
# aws ecr get-login-password --region us-east-1  \
# | docker login \
# --username AWS \
#     --password-stdin 203294716012.dkr.ecr.us-east-1.amazonaws.com


### Manually created the `ai-demo` ECR repo
# docker buildx build --platform linux/amd64 --provenance=false -t ai-demo-product-attribute-extractor:latest .
# docker tag b4f512d1f855 203294716012.dkr.ecr.us-east-1.amazonaws.com/ai-demo:ai-demo-product-attribute-extractor-b4f512d1f855 ->. a367e4943308 is the sha of the image - look into Docker image for the SHA
# docker push 203294716012.dkr.ecr.us-east-1.amazonaws.com/ai-demo:ai-demo-product-attribute-extractor-b4f512d1f855