import json
import logging
import os
import time
from typing import Optional

from openai import OpenAI

from image_attribute_extractor_lambda_hackathon.keyword_analysis import KeywordAnalyzer
from image_attribute_extractor_lambda_hackathon.prompts.base_prompt import api_output_format, schema_org_output_format, \
    mixed_format, attribute_extractor_prompt_non_seo_keywords
from image_attribute_extractor_lambda_hackathon.prompts.base_prompt import attribute_extractor_prompt
from image_attribute_extractor_lambda_hackathon.prompts.categories.dryfood.prompt_market_analysis import \
    golden_standard_attributes as dry_food_golden_standard_attributes
from image_attribute_extractor_lambda_hackathon.prompts.categories.dryfood.prompt_market_analysis import \
    frequently_asked_questions as dry_food_frequently_asked_questions
from image_attribute_extractor_lambda_hackathon.prompts.categories.dryfood.prompt_market_analysis import \
    golden_standard_seo_keyword as dry_food_golden_standard_seo_keyword
from image_attribute_extractor_lambda_hackathon.prompts.categories.dryfood.prompt_market_analysis import \
    pim_attribute_filters as dry_food_pim_attribute_filters

from image_attribute_extractor_lambda_hackathon.prompts.categories.flea_n_tick.prompt_market_analysis import \
    golden_standard_attributes as flea_n_tick_golden_standard_attributes
from image_attribute_extractor_lambda_hackathon.prompts.categories.flea_n_tick.prompt_market_analysis import \
    frequently_asked_questions as flea_n_tick_frequently_asked_questions
from image_attribute_extractor_lambda_hackathon.prompts.categories.flea_n_tick.prompt_market_analysis import \
    golden_standard_seo_keyword as flea_n_tick_golden_standard_seo_keyword
from image_attribute_extractor_lambda_hackathon.prompts.categories.flea_n_tick.prompt_market_analysis import \
    pim_attribute_filters as flea_n_tick_pim_attribute_filters

from image_attribute_extractor_lambda_hackathon.prompts.categories.veterinary_diet.prompt_market_analysis import \
    golden_standard_attributes as veterinary_diet_golden_standard_attributes
from image_attribute_extractor_lambda_hackathon.prompts.categories.veterinary_diet.prompt_market_analysis import \
    frequently_asked_questions as veterinary_diet_frequently_asked_questions
from image_attribute_extractor_lambda_hackathon.prompts.categories.veterinary_diet.prompt_market_analysis import \
    golden_standard_seo_keyword as veterinary_diet_golden_standard_seo_keyword
from image_attribute_extractor_lambda_hackathon.prompts.categories.veterinary_diet.prompt_market_analysis import \
    pim_attribute_filters as veterinary_diet_pim_attribute_filters

from image_attribute_extractor_lambda_hackathon.s3_utils import get_result_from_s3, save_result_to_s3

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

CATEGORY_DRY_FOOD = "dry food"
CATEGORY_FLEA_AND_TICK = "flea and tick"
CATEGORY_VET_DIET = "veterinary diet"

# Global client variable - will be initialized when needed
client = None

"""
Created this file for the Petmeds demo where we are focused on the above 3 categories. 
The category specific SEO keywords and attributes are used to enrich the product   
"""

def get_openai_client():
    """Get or create OpenAI client instance."""
    global client
    if client is None:
        logger.info("Initializing OpenAI client")
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            logger.error("OPENAI_API_KEY environment variable is missing")
            raise ValueError("OPENAI_API_KEY environment variable is required")
        client = OpenAI(api_key=api_key)
    return client


def get_prompt(category: str, responseType: str) -> str:
    """Get the appropriate prompt for the given category."""
    logger.info(f"Getting prompt for category: {category}")
    if responseType == "api":
        resp_output_format = api_output_format
    elif responseType == "schema_org":
        resp_output_format = schema_org_output_format
    else:
        resp_output_format = mixed_format

    resp_output_format = mixed_format
    if category.lower() == CATEGORY_DRY_FOOD:
        prompt = attribute_extractor_prompt.format(golden_standard_attributes=dry_food_golden_standard_attributes,
                                                   golden_standard_seo_keywords=dry_food_golden_standard_seo_keyword,
                                                   pim_attribute_filters=dry_food_pim_attribute_filters,
                                                   frequently_asked_questions=dry_food_frequently_asked_questions,
                                                   output_format=resp_output_format)
    elif category.lower() == CATEGORY_FLEA_AND_TICK:
        prompt = attribute_extractor_prompt.format(golden_standard_attributes=flea_n_tick_golden_standard_attributes,
                                                   golden_standard_seo_keywords=flea_n_tick_golden_standard_seo_keyword,
                                                   pim_attribute_filters=flea_n_tick_pim_attribute_filters,
                                                   frequently_asked_questions=flea_n_tick_frequently_asked_questions,
                                                   output_format=resp_output_format)
    elif category.lower() == CATEGORY_VET_DIET:
        prompt = attribute_extractor_prompt.format(
            golden_standard_attributes=veterinary_diet_golden_standard_attributes,
            golden_standard_seo_keywords=veterinary_diet_golden_standard_seo_keyword,
            pim_attribute_filters=veterinary_diet_pim_attribute_filters,
            frequently_asked_questions=veterinary_diet_frequently_asked_questions,
            output_format=resp_output_format)

    else:
        logger.warning(f"Unsupported category: {category}, falling back to prompt without seo keywords")
        prompt = attribute_extractor_prompt_non_seo_keywords.format(output_format=resp_output_format)

    return prompt

def extract_json_from_ai_response(response_content: str) -> dict:
    """Extract and parse JSON from AI response."""
    logger.debug("Extracting JSON from AI response")
    try:
        parsed_response = json.loads(response_content)
        return parsed_response
    except json.JSONDecodeError as e:
        logger.warning(f"Initial JSON parsing failed: {e}. Attempting to extract JSON using regex.")
        # Try to extract JSON from response if it contains extra text
        import re
        json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
        if json_match:
            try:
                parsed_response = json.loads(json_match.group())
                logger.info("Successfully extracted JSON using regex")
                return parsed_response
            except json.JSONDecodeError:
                logger.error("Failed to parse JSON even after regex extraction")
                pass

        logger.error(f"API returned invalid JSON: {response_content[:100]}...")
        raise ValueError(f"API returned invalid JSON: {response_content[:100]}...")

def product_analyzer(prev_product: str, new_product: str, keywords: str):
    try:
        if not keywords:
            logger.warning("No keywords provided - skipping product analysis")
            return "Not supported as the category is not analysed for search volume keywords"

        logger.info("Analyzing product")
        analyzer = KeywordAnalyzer(json.loads(keywords))
        return analyzer.compare_keyword_counts(prev_product, new_product)
    except Exception as e:
        logger.error(f"Failed to analyze product: {e}")
        return "Unable to analyse the product, please try again"


def extract_attributes(images: list[str], metadata: Optional[dict] = None) -> dict:
    """Extract attributes from product images using OpenAI API."""
    start_time = time.time()

    # Get category from metadata or use default
    category = metadata.get('category', "Dry Food") if metadata else "Dry Food"
    logger.info(f"Extracting attributes for {len(images)} images with category: {category}")
    responseType = metadata.get('responseType', "api") if metadata else "api"

    # Get the appropriate prompt
    attribute_extractor_prompt = get_prompt(category, responseType)

    metadata_string = ""
    if metadata:
        metadata_string = "\n".join(f" {key}: {value}" for key, value in metadata.items())
        logger.debug(f"Metadata included: {', '.join(metadata.keys())}")

    # Prepare content for API request
    content = [{"type": "input_text", "text": attribute_extractor_prompt + metadata_string}]
    for i, image in enumerate(images):
        content.append({
            "type": "input_image",
            "image_url": image,
        })
        logger.debug(f"Added image {i + 1}: {image[:50]}...")

    # Get OpenAI client and make API call
    ai_client = get_openai_client()
    logger.info(f"Sending request to OpenAI API with {len(images)} images")

    try:
        response = ai_client.responses.create(
            model="gpt-4.1",
            input=[{
                "role": "user",
                "content": content,
            }],
        )
        response_time = time.time() - start_time
        logger.info(f"Received response from OpenAI API in {response_time:.2f} seconds")

        # Extract and parse JSON from response
        response_content = response.output_text
        result = extract_json_from_ai_response(response_content)

        # Log success
        total_time = time.time() - start_time
        attribute_count = len(result.get('attributes', [])) if isinstance(result, dict) else 0
        logger.info(f"Successfully extracted {attribute_count} attributes in {total_time:.2f} seconds")

        return result

    except Exception as e:
        logger.error(f"Error calling OpenAI API: {str(e)}")
        raise


def get_seo_keywords(category):
    if category.lower() == CATEGORY_DRY_FOOD:
        return dry_food_golden_standard_seo_keyword
    elif category.lower() == CATEGORY_FLEA_AND_TICK:
        return flea_n_tick_golden_standard_seo_keyword
    elif category.lower() == CATEGORY_VET_DIET:
        return veterinary_diet_golden_standard_seo_keyword


def extract_descriptions_safely(result: dict) -> tuple:
    """
    Safely extract current and generated descriptions from result with fallbacks.

    Args:
        result: The result dictionary from attribute extraction

    Returns:
        tuple: (current_description, generated_description)
    """
    current_description = ""
    generated_description = ""

    # Try to get current description from different possible paths
    if 'description' in result and 'current_value' in result['description']:
        current_description = result['description']['current_value']
    elif 'api_output_format' in result and 'description' in result['api_output_format'] and 'current_value' in \
            result['api_output_format']['description']:
        current_description = result['api_output_format']['description']['current_value']
    else:
        logger.warning("Could not find current description in result")

    # Try to get generated description from different possible paths
    if 'description' in result and 'generated_value' in result['description']:
        generated_description = result['description']['generated_value']
    elif 'api_output_format' in result and 'description' in result['api_output_format'] and 'generated_value' in \
            result['api_output_format']['description']:
        generated_description = result['api_output_format']['description']['generated_value']
    else:
        logger.warning("Could not find generated description in result")

    return current_description, generated_description


def handler(event, context):
    """Lambda handler function."""
    logger.info("Product extractor lambda invoked")

    try:
        # Parse request body
        body = json.loads(event.get("body", "{}"))
        image_urls = body.get("images", [])
        description = body.get("description")
        title = body.get("title")
        sku = body.get("SKU")
        category = body.get("category")
        response_type = body.get("responseFormat") or "api"
        force_refresh = body.get("forceRefresh") or False

        image_urls = list(set(image_urls))

        logger.info(f"Request received - SKU: {sku}, Category: {category}, Images: {len(image_urls)}")

        if not image_urls:
            logger.warning("No images provided in request")
            return {
                "statusCode": 400,
                "body": json.dumps({"error": "No images provided"})
            }

        # Check if we have cached results and force_refresh is not enabled
        if not force_refresh and sku:
            found, cached_result = get_result_from_s3(sku)
            if found and cached_result:
                logger.info(f"Using cached result for SKU: {sku}")
                return {
                    "statusCode": 200,
                    "body": json.dumps(cached_result),
                    "headers": {"X-Cache": "HIT"}
                }

        # Extract attributes
        metadata = {
            "SKU": sku,
            "category": category,
            "title": title,
            "description": description,
            "responseType": response_type
        }
        # Remove None values
        metadata = {k: v for k, v in metadata.items() if v is not None}

        logger.info(f"Extracting attributes for SKU: {sku}")
        result = extract_attributes(image_urls, metadata)

        # Extract descriptions safely
        current_description, generated_description = extract_descriptions_safely(result)

        # Perform keyword analysis only if we have both descriptions
        if current_description and generated_description:
            seo_key_words = get_seo_keywords(category)
            analysis = product_analyzer(current_description, generated_description, seo_key_words)
            result['keyword_analysis'] = analysis
        else:
            logger.warning("Skipping keyword analysis due to missing description data")
            result['keyword_analysis'] = {"error": "Missing description data for analysis"}

        # Save result to S3 for future use if SKU is provided
        if sku:
            save_result_to_s3(sku, result)

        logger.info(f"Successfully processed request for SKU: {sku}")
        return {
            "statusCode": 200,
            "body": json.dumps(result),
            "headers": {"X-Cache": "miss"}
        }

    except ValueError as e:
        logger.error(f"Validation error: {str(e)}")
        return {
            "statusCode": 400,
            "body": json.dumps({"error": str(e)})
        }
    except Exception as e:
        logger.error(f"Failed to extract attributes: {str(e)}", exc_info=True)
        return {
            "statusCode": 500,
            "body": json.dumps({"error": f"Internal server error: {str(e)}"})
        }


# Example usage
if __name__ == "__main__":
    # Example image URLs for testing
    sample_inputs = [
        {
            "metadata": {
                "SKU": "prod60327",
                "category": "dry food",
                "Title": "Wellness Complete Health Large Breed Adult Deboned Chicken & Brown Rice Recipe Dry Dog Food",
                "description": "<h2>Wellness Complete Health Large Breed Adult Deboned Chicken & Brown Rice Recipe Dry Dog Food Directions: </h2>\\n<ul>\\n <li>Please use feeding guidelines as an initial recommendation and adjust as needed.</li>  <li>Every dog is a little different, so optimal feeding amounts may vary with age, size, energy level, breed and environment.</li> \\n</ul>\\n<div class=\"tip\"> <div class=\"name\">Tip:</div><p>A mix of wet and dry dog food can make mealtime more enjoyable for your furry friend. Try pairing with a Wellness Wet Dog Food for variety of different textures they will love.</p></div>\\n<h2>Wellness Complete Health Large Breed Adult Deboned Chicken & Brown Rice Recipe Dry Dog Food Dosage:</h2><table>\\n<caption>Feeding Guidelines</caption>\\n<thead><tr><th scope=\"col\">Weight of Dog</th>\\n<th scope=\"col\">Amount</th>\\n</tr></thead>\\n<tbody><tr><th scope=\"row\">50 - 60 lbs.</th>\\n<td>3¼ - 3⅔ cups</td>\\n</tr>\\n<tr><th scope=\"row\">61 - 70 lbs.</th>\\n<td>3⅔ - 4 cups</td>\\n</tr>\\n<tr><th scope=\"row\">71 - 80 lbs.</th>\\n<td>4 - 4½ cups</td>\\n</tr>\\n<tr><th scope=\"row\">81 - 90 lbs.</th>\\n<td>4½ - 5 cups</td>\\n</tr>\\n<tr><th scope=\"row\">91 - 100 lbs.</th>\\n<td>5 - 5⅓ cups</td>\\n</tr>\\n<tr><th scope=\"row\">101 - 110 lbs.</th>\\n<td>5⅓ - 5¾ cups</td>\\n</tr>\\n<tr><th scope=\"row\">111 - 120 lbs.</th>\\n<td>5¾ - 6 cups</td>\\n</tr>\\n<tr><th scope=\"row\">121 - 140 lbs.</th>\\n<td>6 - 6¾ cups</td>\\n</tr>\\n<tr><th scope=\"row\">141 - 160 lbs.</th>\\n<td>6¾ - 7⅔ cups</td>\\n</tr>\\n<tr><th scope=\"row\">161 - 180 lbs.</th>\\n<td>7⅔ - 8⅓ cups</td>\\n</tr>\\n<tr><th scope=\"row\">Over 180 lbs.</th>\\n<td>Add ⅓ cup per additional 10 lbs.</td>\\n</tr>\\n</tbody>\\n</table>"
            },
            "images": [
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/60327_thumb4/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/60327/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/60327_thumb3/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/60327_thumb5/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/60327_thumb6/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/60327_thumb7/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/60327_thumb2/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/60327_thumb/w=1000,h=1000,q=40"
            ]
        },
        {
            "metadata": {
                "SKU": "prod13345",
                "category": "Flea and Tick",
                "Title": "Nexgard Plus - Flea, Tick, & Broad Spectrum Prevention Chewable Tablet for Dogs",
                "description": "<h2>NexGard<sup>&reg;&nbsp;</sup>PLUS CHEWS For Dogs Directions:</h2>\r\n\r\n<ul>\r\n   <li>NexGard<sup>&reg;</sup> PLUS CHEWS are&nbsp;given orally once a month at the minimum dosage of 1.14 mg/lb (2.5 mg/kg) afoxolaner, 5.45 mcg/lb (12 mcg/kg) moxidectin, and 2.27 mg/lb (5.0 mg/kg) pyrantel (as pamoate salt). Follow the recommended dosing schedule provided for prevention of canine heartworm disease and for the treatment and control of roundworms and hookworms.</li>\r\n    <li>Remove only one chewable at a time from the foil-backed blister card. Return the card with the remaining chewables to its box to protect the product from light.</li>\r\n  <li>NexGard<sup>&reg;</sup> PLUS CHEWS can be administered with or without food. Care should be taken to ensure that the dog consumes the complete dose and that part of the dose is not lost or refused. If a dose is missed, administer NexGard<sup>&reg;</sup> PLUS&nbsp;CHEWS and resume a monthly dosing schedule.</li>\r\n   <li><strong>Heartworm Prevention:&nbsp;</strong>NexGard<sup>&reg;&nbsp;</sup>PLUS&nbsp;CHEWS should be administered at monthly intervals year-round or, at a minimum, administration should start within one month of the dog&rsquo;s first seasonal exposure to mosquitoes and should continue at monthly intervals until at least six months after the dog&rsquo;s last exposure. When replacing another monthly heartworm preventive product, the first dose of NexGard<sup>&reg;</sup> PLUS should be given within a month of the last dose of the former medication.</li>\r\n <li><strong>Flea Treatment and Prevention:</strong>&nbsp;NexGard<sup>&reg;</sup> PLUS CHEWS should be administered year-round at monthly intervals or started at least one month before fleas become active. To minimize the likelihood of flea reinfestation, it is important to treat all animals within a household with an approved flea control product.</li>\r\n <li><strong>Tick Treatment and Control:</strong> NexGard<sup>&reg;</sup> PLUS CHEWS should be administered year-round at monthly intervals or started at least one month before ticks become active.</li>\r\n  <li><strong>Intestinal Nematode Treatment and Control:&nbsp;</strong>NexGard<sup>&reg;</sup> PLUS&nbsp;CHEWS treat&nbsp;and control&nbsp;adult hookworms (Ancylostoma caninum, Ancylostoma braziliense, and Uncinaria stenocephala) and roundworms (Toxocara canis and Toxascaris leonina). For the treatment of adult hookworm and roundworm infections, NexGard<sup>&reg;</sup> PLUS should be administered as a single dose. Monthly use of NexGard<sup>&reg;</sup> PLUS will control any subsequent infections. Dogs may be exposed to and can become infected with hookworms and roundworms throughout the year, regardless of season or climate.</li>\r\n</ul>\r\n\r\n<div class=\"tip\">\r\n<div class=\"name\">Tip:</div>\r\n\r\n<p>Chewables may be broken into pieces and fed to dogs that normally swallow treats whole.</p>\r\n</div>\r\n\r\n<h2>NexGard<sup>&reg;</sup> PLUS&nbsp;CHEWS For Dogs Dosage:</h2>\r\n\r\n<table>\r\n  <caption>Monthly Dosing Schedule</caption>\r\n <thead>\r\n       <tr>\r\n         <th scope=\"col\" style=\"width:50%\">Weight of Dog</th>\r\n         <th scope=\"col\" style=\"width:50%\">Chews Administered</th>\r\n     </tr>\r\n  </thead>\r\n   <tbody>\r\n       <tr>\r\n         <th scope=\"row\">8.1 - 17 lbs</th>\r\n          <td>1 chew</td>\r\n       </tr>\r\n     <tr>\r\n         <th scope=\"row\">17.1 - 33 lbs</th>\r\n         <td>1 chew</td>\r\n       </tr>\r\n     <tr>\r\n         <th scope=\"row\">33.1 - 66 lbs</th>\r\n         <td>1 chew</td>\r\n       </tr>\r\n     <tr>\r\n         <th scope=\"row\">66.1 - 132 lbs</th>\r\n        <td>1 chew</td>\r\n       </tr>\r\n     <tr>\r\n         <th scope=\"row\">Over 132 lbs</th>\r\n          <td>Administer the appropriate combination of chewables</td>\r\n      </tr>\r\n  </tbody>\r\n</table>"
            },
            "images": [
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351_thumb/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351_thumb2/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351_thumb3/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351_thumb4/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351_thumb5/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351_thumb/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351_thumb2/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351_thumb3/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351_thumb4/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13351_thumb5/w=1000,h=1000,q=40"
            ]
        },
        {
            "metadata": {
                "SKU": "prod67559",
                "category": "Veterinary Diet",
                "Title": "Royal Canin Veterinary Diet Canine Skintopic Small Breed Adult Dry Dog",
                "description": "<h2>Royal Canin Veterinary Diet Canine Skintopic Small Breed Adult Dry Dog Food Directions: </h2>\n<ul>\n <li>Consult your veterinarian for specific feeding guidelines for your dog.</li> <li>The daily feeding recommendations below are only a guide.</li> <li>Optimal feeding amounts may vary according to your dog's activity level and environment.</li> <li>Royal Canin recommends that your dog be fed 2 to 4 equal portions of the diet per day.</li> <li>Ensure fresh drinking water is available to your pet at all times.</li> <li>Kibble color may vary due to natural ingredients.</li> <li>When feeding Dry + Wet, this food is best mixed with Royal Canin Veterinary Diet Canine Skintopic Loaf in Sauce Wet Dog Food.</li> \n</ul>\n<div class=\"tip\"> <div class=\"name\">Tip:</div><p>ALLERGEN-AWARE LIVING SPACE: Create an allergen-friendly living space for your dog by minimizing exposure to potential environmental triggers. Regularly clean and vacuum your home to reduce dust and allergens. Use hypoallergenic bedding and consider air purifiers to improve indoor air quality. By creating a cleaner environment, you complement the benefits of Royal Canin Veterinary Diet Canine Skintopic Dog Food in managing environmental food sensitivities for your dog.</p></div>\n<h2>Royal Canin Veterinary Diet Canine Skintopic Small Breed Adult Dry Dog Food Dosage:</h2><table>\n<caption>Daily Feeding Recommendation: Dry Only</caption>\n<thead><tr><th scope=\"col\">Dog Weight</th>\n<th scope=\"col\">Ideal Weight</th>\n<th scope=\"col\">Overweight</th>\n</tr></thead>\n<tbody><tr><th scope=\"row\">4.4 lb</th>\n<td>1/2 cup</td>\n<td>1/2 cup</td>\n</tr>\n<tr><th scope=\"row\">8.8 lb</th>\n<td>7/8 cup</td>\n<td>3/4 cup</td>\n</tr>\n<tr><th scope=\"row\">13 lb</th>\n<td>1 1/8 cups</td>\n<td>1 cup</td>\n</tr>\n<tr><th scope=\"row\">18 lb</th>\n<td>1 1/2 cups</td>\n<td>1 1/4 cups</td>\n</tr>\n<tr><th scope=\"row\">22 lb</th>\n<td>1 3/4 cups</td>\n<td>1 1/2 cups</td>\n</tr>\n</tbody>\n</table>\n<table>\n<caption>Daily Feeding Recommendation: Mix Dry + Wet</caption>\n<thead><tr><th scope=\"col\">Dog Weight</th>\n<th scope=\"col\">Ideal Weight</th>\n<th scope=\"col\">Overweight</th>\n</tr></thead>\n<tbody><tr><th scope=\"row\">4.4 lb</th>\n<td>1/4 cup + 1/4 can</td>\n<td>1/8 cup + 1/4 can</td>\n</tr>\n<tr><th scope=\"row\">8.8 lb</th>\n<td>5/8 cup + 1/4 can</td>\n<td>1/2 cup + 1/4 can</td>\n</tr>\n<tr><th scope=\"row\">13 lb</th>\n<td>5/8 cup + 1/2 can</td>\n<td>1/2 cup + 1/2 can</td>\n</tr>\n<tr><th scope=\"row\">18 lb</th>\n<td>7/8 cup + 1/2 can</td>\n<td>5/8 cup + 1/2 can</td>\n</tr>\n<tr><th scope=\"row\">22 lb</th>\n<td>1 1/8 cups + 1/2 can</td>\n<td>7/8 cup + 1/2 can</td>\n</tr>\n</tbody>\n</table>"
            },
            "images": [
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/67559/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/67559_thumb/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/67559_thumb2/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/67559_thumb3/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/67559_thumb4/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/67559_thumb5/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/67559_thumb6/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/67559_thumb7/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/67559_thumb8/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/67559_thumb9/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/67559_thumb10/w=1000,h=1000,q=40"
            ]
        },
        {
            "images": ["https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13891/w=1000,h=1000,q=40",
                       "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/13891/w=1000,h=1000,q=40",
                       "https://image.chewy.com/catalog/general/images/hills-prescription-diet-dd-skinfood-sensitivities-potato-salmon-recipe-dry-dog-food-25lb-bag/img-426873._AC_SL600_V1_.jpg",
                       "https://image.chewy.com/catalog/general/images/hills-prescription-diet-dd-skinfood-sensitivities-potato-salmon-recipe-dry-dog-food-25lb-bag/img-426874._AC_SL600_V1_.jpg",
                       "https://image.chewy.com/catalog/general/images/hills-prescription-diet-dd-skinfood-sensitivities-potato-salmon-recipe-dry-dog-food-25lb-bag/img-426879._AC_SL600_V1_.jpg"],

            "metadata": {
                "SKU": "prod13891",
                "category": "Veterinary Diet",
                "Title": "Hill's Prescription Diet d/d Food Sensitivities Potato & Salmon Flavor Dry Dog Food",
                "description": "<h2>Hill's Prescription Diet d/d Food Sensitivities Potato & Salmon Flavor Dry Dog Food Directions: </h2>\\r\\n<ul>\\r\\n <li>Ask your veterinarian for specific feeding instructions for your pet.</li> <li>Daily Feeding Recommendations are only a guide and a place to start.</li> <li>New to this food? Mix increasing amounts of your pet's new food with decreasing amounts of the old food for 7 days or more.</li> <li>Exclusively feed the recommended Prescription Diet dry and wet foods.</li> <li>Your pet's nutritional needs may change as they age. Ask your vet at every checkup.</li> \\r\\n</ul>\\r\\n<div class=\"tip\"> <div class=\"name\">Tip:</div><p>Monitor for Improvement: Keep a close eye on your pet's skin, coat, digestion, and overall well-being. Positive changes, such as reduced itching or improved stools, may indicate that the grain-free diet is beneficial for managing food sensitivities.</p></div>\\r\\n<h2>Hill's Prescription Diet d/d Food Sensitivities Potato & Salmon Flavor Dry Dog Food Dosage:</h2><table>\\r\\n<caption>Daily Feeding Recommendations: Adult Maintenance</caption>\\r\\n<thead><tr><th scope=\"col\">Weight of Dog</th>\\r\\n<th scope=\"col\">Amount</th>\\r\\n</tr></thead>\\r\\n<tbody><tr><th scope=\"row\">5 lbs.</th>\\r\\n<td>1/2 cup</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">10 lbs.</th>\\r\\n<td>7/8 cup</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">15 lbs.</th>\\r\\n<td>1 1/4 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">20 lbs.</th>\\r\\n<td>1 1/2 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">30 lbs.</th>\\r\\n<td>2 1/4 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">40 lbs.</th>\\r\\n<td>2 2/3 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">50 lbs.</th>\\r\\n<td>3 1/4 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">60 lbs.</th>\\r\\n<td>3 2/3 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">70 lbs.</th>\\r\\n<td>4 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">80 lbs.</th>\\r\\n<td>4 1/2 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">100 lbs.</th>\\r\\n<td>5 1/4 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">120 lbs.</th>\\r\\n<td>6 cups</td>\\r\\n</tr>\\r\\n</tbody>\\r\\n</table>",
                "requestType": "product_extractor", "response_type": "mixed"}
        },
        {
            "images": [
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094_thumb/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094_thumb2/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094_thumb3/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094_thumb4/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094_thumb5/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094_thumb/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094_thumb2/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094_thumb3/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094_thumb4/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/61094_thumb5/w=1000,h=1000,q=40"
            ],
            "metadata": {
            "SKU": "prod61094",
            "category": "Dry Food",
            "Title": "Forza10 Nutraceutic Active Kidney Renal Support Diet Dry Dog Food",
            "description": "<h2>Forza10 Nutraceutic Active Kidney Renal Support Diet Dry Dog Food Directions: </h2>\\n<ul>\\n <li>Use the recommended daily feeding instructions to ensure your dog's optimal nutrition.</li>  <li>The suggested amount is necessary to maintain your dog's ideal body condition.</li>  <li>Individual dog requirements may vary based on age, breed, environment, and activity level.</li>  <li>Adjust their food intake as necessary to maintain their optimal body condition.</li> \\n</ul>\\n<div class=\"tip\"> <div class=\"name\">Tip:</div><p>Transition Instructions: This product can be used without any adjustment period.</p></div>\\n<h2>Forza10 Nutraceutic Active Kidney Renal Support Diet Dry Dog Food Dosage:</h2><table>\\n<caption>Daily Feeding Instructions</caption>\\n<thead><tr><th scope=\"col\">Weight</th>\\n<th scope=\"col\">Amount</th>\\n</tr></thead>\\n<tbody><tr><th scope=\"row\">4 - 11 lbs.</th>\\n<td>1 - 1/2 cup</td>\\n</tr>\\n<tr><th scope=\"row\">12 - 20 lbs.</th>\\n<td>1 1/4 - 1 1/2 cups</td>\\n</tr>\\n<tr><th scope=\"row\">21 - 35 lbs.</th>\\n<td>1 1/2 - 2 1/2 cups</td>\\n</tr>\\n<tr><th scope=\"row\">36 - 50 lbs.</th>\\n<td>2 1/2 - 3 1/4 cups</td>\\n</tr>\\n<tr><th scope=\"row\">51 - 75 lbs.</th>\\n<td>3 1/4 - 4 1/4 cups</td>\\n</tr>\\n<tr><th scope=\"row\">76 - 100 lbs.</th>\\n<td>4 1/4 - 5 1/4 cups</td>\\n</tr>\\n<tr><th scope=\"row\">Over 100 lbs.</th>\\n<td>5 1/2 + 1/2 cups</td>\\n</tr>\\n</tbody>\\n</table>",
            "requestType": "product_extractor",
            "responseType": "mixed"
            }
        },
        { "images": ["https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/10372/w=1000,h=1000,q=40",
                                          "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/10372/w=1000,h=1000,q=40"],
          "metadata": {
         "SKU": "prod10356", "category": "Flea and Tick",
         "Title": "NexGard - Flea & Tick Prevention Chewable Tablets for Dogs",
         "description": "<h2>NexGard Pill for Dogs Chewables Directions:</h2>\\r\\n<ul>\\r\\n\t<li>NexGard (afoxolaner) is given as a single dose orally once a month without interruption, at the minimum dosage of 1.14 mg/lb (2.5 mg/kg).</li>\\r\\n\t<li>NexGard can be administered with or without food. Care should be taken that the dog consumes the complete and correct dose, and treated animals should be observed for a few minutes to ensure that part of the dose is not lost or refused. If it is suspected that any of the dose has been lost or if vomiting occurs within two hours of administration, redose with another full dose. If a dose is missed, administer NexGard and resume a monthly dosing administration.</li>\\r\\n\t<li>Treatment Against Fleas and Prevention: Oral flea reatment with NexGard may begin at any time of the year. In areas where fleas are common year-round, monthly treatment with NexGard should continue the entire year without interruption.</li>\\r\\n\t<li>To minimize the likelihood of flea reinfestation, it is important to treat all pet animals within a household with an approved flea control product.</li>\\r\\n\t<li>Tick Treatment and Control: Treatment with NexGard Flea & Tick Chewables may begin at any time for year-round protection.</li>\\r\\n</ul>\\r\\n\\r\\n<div class=\"tip\">\\r\\n\t<div class=\"name\">Tip:</div>\\r\\n\t<p>For best results, make sure that your dog eats the entire chew.</p>\\r\\n</div> \\r\\n\\r\\n<h2>NexGard Chewables Dosage Chart for Dogs:</h2>\\r\\n<table>\\r\\n\t<caption>Dogs/Puppies: (over 8 weeks of age)</caption>\\r\\n\t<thead>\\r\\n\t\t<tr>\\r\\n\t\t\t<th scope=\"col\">Weight</th>\\r\\n\t\t\t<th scope=\"col\">Dosage</th>\\r\\n\t\t</tr>\\r\\n\t</thead>\\r\\n\t<tbody>    \\r\\n\t\t<tr>\\r\\n\t\t\t<th scope=row> 4-10 lbs</th>\\r\\n\t\t\t<td>Administer one chew</td>\\r\\n\t\t</tr>\\r\\n\t\t<tr>\\r\\n\t\t\t<th scope=row> 10.1-24 lbs</th>\\r\\n\t\t\t<td>Administer one chew</td>\\r\\n\t\t</tr>\\r\\n\t\t<tr>\\r\\n\t\t\t<th scope=row> 24.1-60 lbs</th>\\r\\n\t\t\t<td>Administer one chew</td>\\r\\n\t\t</tr>\\r\\n\t\t<tr>\\r\\n\t\t\t<th scope=row> 60.1-121 lbs</th>\\r\\n\t\t\t<td>Administer one chew</td>\\r\\n\t\t</tr>\\r\\n\t\t<tr>\\r\\n\t\t\t<th scope=row> Over 121 lbs</th>\\r\\n\t\t\t<td>Administer the appropriate combination of chewables</td>\\r\\n\t\t</tr>\\r\\n\t</tbody>\\r\\n</table>  \\r\\n<table>\\r\\n\t<caption>NexGard Chewables Dosage for Cats:</caption>\\r\\n\t<thead>\\r\\n\t\t<tr>\\r\\n\t\t\t<th scope=\"col\">Cats</th>\\r\\n\t\t</tr>\\r\\n\t</thead>\\r\\n\t<tbody>\\r\\n\t\t<th scope=row> Do not use!</th>\\r\\n\t</tbody>\\r\\n</table>  \\r\\n<table>\\r\\n\t<caption>NexGard Chewables Dosage for Horses:</caption>\\r\\n\t<thead>\\r\\n\t\t<tr>\\r\\n\t\t\t<th scope=\"col\">Horses</th>\\r\\n\t\t</tr>\\r\\n\t</thead>\\r\\n\t<tbody>\\r\\n\t\t<th scope=row> Do not use!</th>\\r\\n\t</tbody>\\r\\n</table>",
         "requestType": "product_extractor", "responseType": "mixed", "forceRefresh": True,
          }
          },
        {

            "images": [
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/65341/w=1000,h=1000,q=40",
                "https://imagedelivery.net/OWeDZSTbTyzlX4Fsq0SDAw/65341/w=1000,h=1000,q=40",
                "https://image.chewy.com/catalog/general/images/canine-caviar-limited-ingredient-diet-wild-ocean-holistic-entre-all-life-stages-grain-free-dry-dog-food-11lb-bag/img-778860._AC_SL600_V1_.jpg",
                "https://image.chewy.com/catalog/general/images/canine-caviar-limited-ingredient-diet-wild-ocean-holistic-entre-all-life-stages-grain-free-dry-dog-food-11lb-bag/img-778861._AC_SL600_V1_.jpg",
                "https://image.chewy.com/catalog/general/images/canine-caviar-limited-ingredient-diet-wild-ocean-holistic-entre-all-life-stages-grain-free-dry-dog-food-11lb-bag/img-778862._AC_SL600_V1_.jpg",
                "https://image.chewy.com/catalog/general/images/canine-caviar-limited-ingredient-diet-wild-ocean-holistic-entre-all-life-stages-grain-free-dry-dog-food-11lb-bag/img-778864._AC_SL600_V1_.jpg"
            ],
            "metadata": {
            "SKU": "prod65297",
            "category": "Dry Food",
            "Title": "Canine Caviar Wild Ocean Holistic Grain Free Entree Dry Food",
            "description": "<h2>Canine Caviar Wild Ocean Holistic Grain Free Entree Dry Food Directions:</h2>\\r\\n\\r\\n<ul>\\r\\n\t<li>To get the best results, it is important to feed properly.</li>\\r\\n\t<li>Your dog should be feed 1/2 of the daily portion in the morning and 1/2 in the evening.</li>\\r\\n\t<li>Do not mix with your current food.</li>\\r\\n\t<li>We recommend a straight changeover to Canine Caviar, feeding 1/2 the recommended amount the first day.</li>\\r\\n\t<li>The feeding portions may vary according to age, breed, activity level and environment.</li>\\r\\n\t<li>Adjust accordingly to maintain optimal body condition.</li>\\r\\n</ul>\\r\\n\\r\\n<div class=\"tip\">\\r\\n<div class=\"name\">Tip:</div>\\r\\n\\r\\n<p>Always have plenty of fresh water available!</p>\\r\\n</div>",
            "requestType": "product_extractor",
            "responseFormat": "mixed"
            }
        }

    ]

    sample_input = sample_inputs[-1]
    sku = "prod65297"
    force_refresh = False

    if not force_refresh and sku:
        found, cached_result = get_result_from_s3(sku)
        if found and cached_result:
            logger.info(f"Using cached result for SKU: {sku}")
            print({
                "statusCode": 200,
                "body": json.dumps(cached_result),
                "headers": {"X-Cache": "HIT"}
            })

    try:

        # op = """{"title": {"current_value": "Wellness Complete Health Large Breed Adult Deboned Chicken & Brown Rice Recipe Dry Dog Food", "generated_value": "Wellness Complete Health Large Breed Adult Dry Dog Food with Deboned Chicken & Brown Rice, High-Protein Natural Kibble for Joint Support & Healthy Digestion, Ideal for Large Breed Adult Dogs", "metadata": {"reasoning": "Followed the recommended title formula: Brand first, primary keywords ('Dry Dog Food', 'Large Breed', 'Chicken & Brown Rice'), highlighted top features/benefits (high-protein, joint support, healthy digestion), and specified main use case (large breed adult dogs). Incorporated power SEO keywords: 'dry dog food', 'large breed', 'chicken', 'high-protein', and 'natural dog food' for maximum discoverability and SEO impact.", "sources": ["current title", "Image2", "Image4", "product description", "Golden Standard SEO keywords"], "status": "updated"}}, "images": [{"current_value": "Image1.jpg", "alt": "Feeding chart for Wellness Complete Health Large Breed Adult Deboned Chicken & Brown Rice Recipe Dry Dog Food, showing cups and grams per day based on dog weight ranges."}, {"current_value": "Image2.jpg", "alt": "Front view of Wellness Complete Health Large Breed Adult Dog Food package featuring key product highlights and information for large breed adult dogs."}, {"current_value": "Image3.jpg", "alt": "Marketing information emphasizing 5 signs of wellbeing and veterinarians' recommendation for Wellness dog food."}, {"current_value": "Image4.jpg", "alt": "Ingredients list for Wellness Complete Health Large Breed Adult Deboned Chicken & Brown Rice Recipe Dog Food, including Deboned Chicken, Brown Rice, Peas, and more."}, {"current_value": "Image5.jpg", "alt": "Guaranteed analysis panel for Wellness Complete Health Large Breed Adult Dog Food, listing levels of protein, fat, fiber, vitamins, and minerals."}, {"current_value": "Image6.jpg", "alt": "Graphic with three Wellness Complete Health dog food bags for Puppy, Adult, and Senior formulas, highlighting life stage suitability."}, {"current_value": "Image7.jpg", "alt": "Nutritional highlights of Wellness Complete Health Large Breed Adult Dog Food featuring deboned chicken, blueberries, flaxseed, and spinach."}, {"current_value": "Image8.jpg", "alt": "Back of the Wellness Complete Health Large Breed Adult Dog Food bag with feeding instructions and key benefit icons."}], "description": {"current_value": "<h2>Wellness Complete Health Large Breed Adult Deboned Chicken & Brown Rice Recipe Dry Dog Food Directions: </h2>\\n<ul>\\n <li>Please use feeding guidelines as an initial recommendation and adjust as needed.</li>  <li>Every dog is a little different, so optimal feeding amounts may vary with age, size, energy level, breed and environment.</li> \\n</ul>\\n<div class=\\"tip\\"> <div class=\\"name\\">Tip:</div><p>A mix of wet and dry dog food can make mealtime more enjoyable for your furry friend. Try pairing with a Wellness Wet Dog Food for variety of different textures they will love.</p></div>\\n<h2>Wellness Complete Health Large Breed Adult Deboned Chicken & Brown Rice Recipe Dry Dog Food Dosage:</h2><table>\\n<caption>Feeding Guidelines</caption>\\n<thead><tr><th scope=\\"col\\">Weight of Dog</th>\\n<th scope=\\"col\\">Amount</th>\\n</tr></thead>\\n<tbody><tr><th scope=\\"row\\">50 - 60 lbs.</th>\\n<td>3¼ - 3⅔ cups</td>\\n</tr>\\n<tr><th scope=\\"row\\">61 - 70 lbs.</th>\\n<td>3⅔ - 4 cups</td>\\n</tr>\\n<tr><th scope=\\"row\\">71 - 80 lbs.</th>\\n<td>4 - 4½ cups</td>\\n</tr>\\n<tr><th scope=\\"row\\">81 - 90 lbs.</th>\\n<td>4½ - 5 cups</td>\\n</tr>\\n<tr><th scope=\\"row\\">91 - 100 lbs.</th>\\n<td>5 - 5⅓ cups</td>\\n</tr>\\n<tr><th scope=\\"row\\">101 - 110 lbs.</th>\\n<td>5⅓ - 5¾ cups</td>\\n</tr>\\n<tr><th scope=\\"row\\">111 - 120 lbs.</th>\\n<td>5¾ - 6 cups</td>\\n</tr>\\n<tr><th scope=\\"row\\">121 - 140 lbs.</th>\\n<td>6 - 6¾ cups</td>\\n</tr>\\n<tr><th scope=\\"row\\">141 - 160 lbs.</th>\\n<td>6¾ - 7⅔ cups</td>\\n</tr>\\n<tr><th scope=\\"row\\">161 - 180 lbs.</th>\\n<td>7⅔ - 8⅓ cups</td>\\n</tr>\\n<tr><th scope=\\"row\\">Over 180 lbs.</th>\\n<td>Add ⅓ cup per additional 10 lbs.</td>\\n</tr>\\n</tbody>\\n</table>", "generated_value": "<h2>Wellness Complete Health Large Breed Adult Dry Dog Food — Natural, High-Protein Kibble with Deboned Chicken & Brown Rice</h2>\\n<ul>\\n<li>Specially formulated for large breed adult dogs, Wellness Complete Health provides balanced nutrition with high-quality deboned chicken as the #1 ingredient, supporting strong muscles and optimal energy for active big dogs.</li>\\n<li>Enriched with brown rice, peas, barley, and wholesome grains for easily digestible carbohydrates and sustained energy, making it great for maintaining healthy weight in large breeds.</li>\\n<li>Fortified with essential vitamins, minerals, probiotics, and guaranteed levels of glucosamine and chondroitin sulfate to support joint health and mobility in large and giant breeds.</li>\\n<li>Promotes digestive health, immune function, and a shiny coat, thanks to omega-rich flaxseed, spinach, blueberries, and natural chicken fat preserved with mixed tocopherols.</li>\\n<li>No meat by-products, fillers, or artificial preservatives. Proudly made in the USA.</li>\\n<li>Feeding guidelines ensure you're supporting ideal body weight. See chart for detailed recommendations based on your dog's weight. (See images for complete feeding chart.)</li>\\n</ul>\\n<p>Wellness dog food is veterinarian recommended for providing proven nutrition. Natural ingredients and super nutrients help support the 5 signs of wellbeing, including optimal energy, healthy digestion, healthy skin & coat, immune health, and strong teeth & bones.</p>\\n<p>Transition to Wellness and discover why pet parents trust this best dog food brand for healthy, happy large breed companions. Perfect for customers searching for <b>dog food</b>, <b>dry dog food</b>, <b>best dog food</b>, <b>healthy dog food</b>, and <b>large breed adult kibble</b>.</p>", "metadata": {"reasoning": "Transformed the description to include key benefits, ingredients, and health features directly observed from the images and text. Incorporated high-volume SEO keywords like 'dog food', 'dry dog food', 'best dog food', and 'healthy dog food' for maximum SEO impact, kept structure with bullet points and HTML for engagement and consistency.", "sources": ["current description", "Image2", "Image3", "Image4", "Image5", "Image7", "Golden Standard SEO keywords"], "status": "updated", "seoKeywords": [{"keyword": "dog food", "volume": 135000}, {"keyword": "best dog food", "volume": 40500}, {"keyword": "dry dog food", "volume": 8100}, {"keyword": "healthy dog food", "volume": 8100}, {"keyword": "large breed", "volume": 6600}]}}, "attributes": {"Brand": {"current_value": "", "generated_value": "Wellness", "metadata": {"reasoning": "Brand 'Wellness' clearly visible on front of packaging in multiple images.", "sources": ["Image2", "Image8", "description"], "status": "new"}}, "Lifestage": {"current_value": "", "generated_value": "Adult", "metadata": {"reasoning": "Clearly stated 'Large Breed Adult' on packaging and description.", "sources": ["Image2", "Image8", "current title"], "status": "new"}}, "Breed Size": {"current_value": "", "generated_value": "Large Breeds", "metadata": {"reasoning": "Product is formulated for 'Large Breed' as per package front and title.", "sources": ["Image2", "current title"], "status": "new"}}, "Special Diet": {"current_value": "", "generated_value": "Natural", "metadata": {"reasoning": "'Natural food for dogs' wording seen on packaging and in ingredient highlights, and included as accepted value in golden standard.", "sources": ["Image2", "Image7"], "status": "new"}}, "Health Feature": {"current_value": "", "generated_value": "Hip & Joint Support", "metadata": {"reasoning": "Packaging indicates support for Hip & Joint Health with added glucosamine and chondroitin sulfate.", "sources": ["Image2", "Image8"], "status": "new"}}, "Flavor": {"current_value": "", "generated_value": "Chicken", "metadata": {"reasoning": "Listed as 'Deboned Chicken & Brown Rice Recipe' and ingredients section confirms chicken as primary animal protein.", "sources": ["current title", "Image2", "Image4"], "status": "new"}}, "Packaging Type": {"current_value": "", "generated_value": "Bag", "metadata": {"reasoning": "Product is clearly a traditional resealable dog food bag.", "sources": ["Image2", "Image8"], "status": "new"}}, "Food Form": {"current_value": "", "generated_value": "Dry Food", "metadata": {"reasoning": "Indicated as 'Dry Dog Food' in both title and packaging.", "sources": ["current title", "Image2", "description"], "status": "new"}}, "Grain Content": {"current_value": "", "generated_value": "Brown Rice", "metadata": {"reasoning": "Recipe contains brown rice, oats, barley as seen in ingredients list and title.", "sources": ["current title", "Image4"], "status": "new"}}, "Key Benefits": {"current_value": "", "generated_value": "Supports hip & joint health, promotes healthy weight, optimal energy, healthy digestion, shiny coat, and immune system; made with natural ingredients and deboned chicken as the first ingredient.", "metadata": {"reasoning": "Summarized the multiple benefit points highlighted across brand imagery and marketing copy.", "sources": ["Image2", "Image3", "Image7", "description"], "status": "new"}}, "SKU": {"current_value": "", "generated_value": "prod60327", "metadata": {"reasoning": "SKU explicitly provided in the structured input.", "sources": ["input data"], "status": "new"}}, "Ingredients": {"current_value": "", "generated_value": "Deboned Chicken, Chicken Meal, Ground Brown Rice, Barley, Peas, Oats, Chicken Fat (preserved with Mixed Tocopherols), Salmon Meal, Tomato Pomace, Oatmeal, Tomatoes, Carrots, Natural Chicken Flavor, Ground Flaxseed, Choline Chloride, Spinach, Vitamin E Supplement, Taurine, Zinc Proteinate, Mixed Tocopherols added to preserve freshness, Sweet Potatoes, Apples, Blueberries, Zinc Sulfate, Calcium Carbonate, Niacin, Ferrous Sulfate, Iron Proteinate, Vitamin A Supplement, Glucosamine Hydrochloride, Chondroitin Sulfate, Ascorbic Acid (Vitamin C), Copper Sulfate, Thiamine Mononitrate, Copper Proteinate, Chicory Root Extract, Manganese Proteinate, Manganese Sulfate, d-Calcium Pantothenate, Sodium Selenite, Pyridoxine Hydrochloride, Riboflavin, Yucca Schidigera Extract, Garlic Powder, Vitamin D3 Supplement, Biotin, Calcium Iodate, Vitamin B12 Supplement, Folic Acid, Dried Lactobacillus plantarum Fermentation Product, Dried Enterococcus faecium Fermentation Product, Dried Lactobacillus casei Fermentation Product, Dried Lactobacillus acidophilus Fermentation Product, Rosemary Extract, Green Tea Extract, Spearmint Extract.", "metadata": {"reasoning": "Exact ingredients list fully extracted from high-resolution image.", "sources": ["Image4"], "status": "new"}}, "Guaranteed Analysis": {"current_value": "", "generated_value": "Crude Protein (min): 26.0%; Crude Fat (min): 12.0%; Crude Fiber (max): 5.0%; Moisture (max): 10.0%; Calcium (min): 1.20%; Phosphorus (min): 0.80%; Vitamin A (min): 25,000 IU/kg; Vitamin E (min): 200 IU/kg; Omega-6 Fatty Acids (min): 2.00%; Omega-3 Fatty Acids (min): 0.75%; Glucosamine (min): 750 mg/kg; Chondroitin Sulfate (min): 250 mg/kg; Taurine (min): 0.09%; Total Lactic Acid Microorganism (min): 20,000,000 CFU/lb", "metadata": {"reasoning": "Direct transcription from the Guaranteed Analysis image for data accuracy and completeness.", "sources": ["Image5"], "status": "new"}}, "Caloric Content": {"current_value": "", "generated_value": "3,400 kcal/kg; 340 kcal/cup (ME calculated as fed)", "metadata": {"reasoning": "Value clearly stated on the feeding chart image (Image1).", "sources": ["Image1"], "status": "new"}}, "Country of Origin": {"current_value": "", "generated_value": "USA", "metadata": {"reasoning": "Text and product packaging confirm the product is made in the USA.", "sources": ["Image2", "Image8"], "status": "new"}}, "Feeding Instructions": {"current_value": "", "generated_value": "Refer to the feeding chart for recommended daily servings based on your dog's weight. For combination feeding with wet food, decrease dry food by 1/3 cup per 6 oz of wet food. Adjust as needed for age, activity, breed, and environment.", "metadata": {"reasoning": "Extracted specific instructions displayed on both the feeding info image and in the text description.", "sources": ["Image1", "current description"], "status": "new"}}, "Transition Instructions": {"current_value": "", "generated_value": "Gradually mix increasing amounts of Wellness Complete Health Large Breed Adult Dog Food with your dog's current food over seven days.", "metadata": {"reasoning": "Standard transition guidelines are usually recommended for dry dog food when not specifically stated, inferred confidently from category best practices.", "sources": ["Inference from category", "current description"], "status": "new"}}, "Activity Level": {"current_value": "", "generated_value": "Active", "metadata": {"reasoning": "Formulated for large breed adults with sustained energy and optimal nutritional profile suggesting active dogs.", "sources": ["Image2", "Image7", "description"], "status": "new"}}, "Food Top Category": {"current_value": "", "generated_value": "Food", "metadata": {"reasoning": "Product is a primary food, not a treat or supplement.", "sources": ["category"], "status": "new"}}, "Pet Size": {"current_value": "", "generated_value": "large", "metadata": {"reasoning": "Indicated for large breed dogs throughout all visuals and text.", "sources": ["Image2", "Image8", "current title"], "status": "new"}}, "Product Name": {"current_value": "", "generated_value": "Wellness Complete Health Large Breed Adult Deboned Chicken & Brown Rice Recipe Dry Dog Food", "metadata": {"reasoning": "Product name as provided and validated by packaging.", "sources": ["current title", "Image2"], "status": "new"}}}}"""
        # result = op
        # Extract attributes with product description
        image_urls = list(set(sample_input['images']))
        result = extract_attributes(image_urls, sample_input['metadata'])
        image_urls = image_urls
        metadata = sample_input['metadata']
        category = metadata.get('category', "Dry Food") if metadata else "Dry Food"
        seo_key_words = get_seo_keywords(category)
        current_description, generated_description = extract_descriptions_safely(result)

        # Perform keyword analysis only if we have both descriptions
        if current_description and generated_description:
            seo_key_words = get_seo_keywords(category)
            analysis = product_analyzer(current_description, generated_description, seo_key_words)
            result['keyword_analysis'] = analysis
        else:
            logger.warning("Skipping keyword analysis due to missing description data")
            result['keyword_analysis'] = {"error": "Missing description data for analysis"}
        print(json.dumps(result, indent=2))
        if sku:
            save_result_to_s3(sku, result)

    except Exception as e:
        logger.error(f"Failed to extract attributes: {e}")
        print(f"Error: {e}")
