import re
from typing import List, Dict, Tuple

class KeywordAnalyzer:
    def __init__(self, keywords: List[Dict[str, int]]):
        """
        keywords: List of dicts, each with 'keyword' and 'volume' keys.
        Example: [{"keyword": "dog food", "volume": 101879}, ...]
        """
        self.keywords = keywords

    def count_occurrences(self, text: str, keyword: str) -> int:
        pattern = r'\b' + re.escape(keyword) + r'\b'
        return len(re.findall(pattern, text, flags=re.IGNORECASE))

    def compare_keyword_counts(self, before: str, after: str) -> List[Dict[str, int]]:
        results = []
        for kw in self.keywords:
            keyword = kw["keyword"]
            volume = kw.get("volume", None)
            data = {
                'keyword': keyword,
                'volume': volume,
                'before': self.count_occurrences(before, keyword),
                'after': self.count_occurrences(after, keyword)
            }
            if not (data['before'] == 0 and data['after'] == 0):
                results.append(data)
        return results

# Example usage:
if __name__ == "__main__":
    before_text = "<h2><PERSON><PERSON><PERSON>y Weight with Real Farm-Raised Chicken Dry Dog Food Directions: </h2>\\r\\n<ul>\\r\\n <li>The recommended feeding guidelines are based on using a standard 8 oz measuring cup. </li>  <li>Most adult dogs can be fed once daily.  However, it may be beneficial to divide the recommended daily amounts into two meals. </li>  <li>Making the Switch to Beneful: When switching your dog to Beneful Originals dog food, please allow 7 - 10 days for the transition. Gradually add more Beneful Originals and less of the previous food to your dog\u2019s dish each day until the changeover is complete. This gradual transition will help avoid dietary upsets.</li> \\r\\n</ul>\\r\\n<div class=\"tip\"> <div class=\"name\">Tip:</div><p>The food intake required to maintain ideal body condition will vary depending on age, activity and environment, and should be adjusted accordingly.</p></div>\\r\\n<h2>Purina Beneful Healthy Weight with Real Farm-Raised Chicken Dry Dog Food Dosage:</h2><table>\\r\\n<caption>Feeding Guidelines</caption>\\r\\n<thead><tr><th scope=\"col\">Current Weight</th>\\r\\n<th scope=\"col\">Weight Maintenance</th>\\r\\n<th scope=\"col\">Weight Reduction</th>\\r\\n</tr></thead>\\r\\n<tbody><tr><th scope=\"row\">3 - 12 lbs.</th>\\r\\n<td>1/2 - 1 1/4 cups</td>\\r\\n<td>1/3 - 1 cup</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">13 - 20 lbs.</th>\\r\\n<td>1 1/4 - 1 2/3 cups</td>\\r\\n<td>1 - 1 1/3 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">21 - 35 lbs.</th>\\r\\n<td>1 3/4 - 2 1/2 cups</td>\\r\\n<td>1 1/3 - 2 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">36 - 50 lbs.</th>\\r\\n<td>2 1/2 - 3 1/4 cups</td>\\r\\n<td>2 - 2 1/2 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">51 - 75 lbs.</th>\\r\\n<td>3 1/4 - 4 1/4 cups</td>\\r\\n<td>2 2/3 - 3 1/3 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">76 - 100 lbs.</th>\\r\\n<td>4 1/4 - 4 3/4 cups</td>\\r\\n<td>3 1/3 - 4 cups</td>\\r\\n</tr>\\r\\n<tr><th scope=\"row\">Over 100  lbs.</th>\\r\\n<td>4 3/4 cups plus 1/3 cup for each 10 lbs. over 100 lbs.</td>\\r\\n<td>4 cups plus 1/4 cup for each 10 lbs. over 100 lbs.</td>\\r\\n</tr>\\r\\n</tbody>\\r\\n</table>"
    after_text = "Purina Beneful Healthy Weight with Real Farm-Raised Chicken is premium dry dog food designed specifically for adult dogs needing effective weight management. Made with real farm-raised chicken as the #1 ingredient, this kibble provides calorie-smart nutrition to help maintain a healthy weight without sacrificing taste. Every bite is rich in protein to support strong muscles and features whole grains, apples, carrots, and green beans for complete nutrition. With no artificial preservatives, flavors, or colors and added colors only from natural sources, it\u2019s a wholesome, natural dog food your pet will love. Each cup packs 25g of high-quality protein, along with a blend of 23 essential vitamins and minerals for overall canine health. Beneful Healthy Weight is 100% complete and balanced for adult dogs, formulated to meet AAFCO Dog Food Nutrient Profiles. Prepared in Purina\u2019s USA facilities, this best dog food brand supports weight control while optimizing taste, quality, and well-being for your dog. If you're searching for the best dry dog food, high protein dog food, or calorie control for adult dogs, Purina Beneful is an excellent choice."
    keywords = [
        {"keyword": "dog food", "volume": 101879},
        {"keyword": "dry dog food", "volume": 50000},
        {"keyword": "dog", "volume": 200000},
        {"keyword": "food", "volume": 150000},
        {"keyword": "healthy", "volume": 30000}
    ]

    analyzer = KeywordAnalyzer(keywords)
    result = analyzer.compare_keyword_counts(before_text, after_text)
    print(result)