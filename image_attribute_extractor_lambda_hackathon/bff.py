import base64
import os
import json
import tempfile
import time
import logging
import openai
import traceback

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize client variable
client = None

core_prompt = prompt_text = """
    Extract product information from the provided HTML. 
    You are a strict JSON API. Always respond with a valid JSON object.
    Do not include any explanations, markdown, or extra text.

    Example:
    {
      "title": "Smartphone",
      "description": "A powerful Android smartphone with a long-lasting battery.",
      "tags": ["electronics", "smartphone", "android"]
    }

    Now a JSON object with the following fields: 
    isPDP - boolean indicating if the url is a PDP
    title - string containing the product title
    titleSelector - DOM querySelector to target element containing the product title in JS. This needs to be accurate and specific to be able to target using js. Should have at least 1 id,class or property in the selector
    sku - sku identifier of the product as string
    price - price of the product with currency as string
    category - string indicating category breadcrumb of product. Cannot contain ellipses. Ignore the first level like Home  e.g. "Electronics > Appliances"
    brand - Brand name of the product
    rating - numeric rating value of the product
    reviews - numeric count of the total number of reviews
    inStock - boolean indicating if the product is in stock or not
    weight - string with product weight and unit
    imageUrl - Product's main image. Should be a valid https URL
    description - Product details or description.
    descriptionSelector - DOM querySelector to target element containing the product description in JS. This needs to be accurate and specific to be able to target using js. Should have at least 1 id,class or property in the selector
    attributes - An array of object. Each object to have attributeName: name of the attribute, attributeValue: value of that attribute, selector: DOM querySelector to target parent element which contains both attribute name and value. Selector needs to be accurate and specific to be able to target using js. Should have at least 1 id,class or property in the selector
    returnPolicySelector - DOM querySelector to target element containing the product's return policy in JS.
    """


def get_openai_client():
    global client
    if client is None:
        api_key = os.environ.get('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable not set")
        client = openai.OpenAI(api_key=api_key)
    return client


def extract_product_info_from_html(html_bytes):
    logger.info("Starting product info extraction from HTML")
    client = get_openai_client()

    temp_file_path = None
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".html", mode="wb") as temp_file:
            temp_file.write(html_bytes)
            temp_file_path = temp_file.name
        logger.info(f"HTML content saved to temporary file: {temp_file_path}")

        # Upload file for the assistant's purpose
        with open("testDom.txt", "rb") as f:
            logger.info("Uploading file to OpenAI")
            uploaded_file = client.files.create(file=f, purpose="assistants")
            print(f"File uploaded successfully: {uploaded_file.id}")
            logger.info(f"File uploaded successfully: {uploaded_file.id}")

        # Create assistant
        assistant = client.beta.assistants.create(
            model="gpt-4o",
            name="Product Info Extractor",
            instructions="You are an expert at extracting information from HTML documents. Read the provided HTML file using the code interpreter and extract the product title, price, description, and all image URLs. Present the extracted information as a JSON object with the following keys: 'title', 'price', ' 'description', and 'image_urls' (which should be a list of strings). If a piece of information is not found, use 'N/A' or an empty list for 'image_urls'.",
            tools=[{"type": "code_interpreter"}]
        )

        # Create thread
        thread = client.beta.threads.create()

        # Create message with explicit reference to the file_id in content for the code interpreter
        # The key is to tell the assistant IN TEXT that it should use the file and its ID.
        # The file itself is attached to the message using the 'file_ids' parameter,
        # but the content itself still needs to be text or an image.
        # For a non-image file, the best way is to instruct the assistant with the file_id.
        client.beta.threads.messages.create(
            thread_id=thread.id,
            role="user",
            content=[
                {"type": "text",
                 "text": f"{core_prompt}. Please extract the product information from the HTML file with ID: {uploaded_file.id}. Use the code interpreter to parse the HTML."}
            ],
            attachments=[{"file_id": uploaded_file.id, "tools": [{"type":"code_interpreter"}]}]  # This is the correct way to link files to a message for the assistant to use
        )

        # Start run
        run = client.beta.threads.runs.create(
            thread_id=thread.id,
            assistant_id=assistant.id
        )

        # Wait for completion
        start_time = time.time()
        while True:
            run_status = client.beta.threads.runs.retrieve(thread_id=thread.id, run_id=run.id)
            if run_status.status in ["completed", "failed", "cancelled"]:
                break
            if time.time() - start_time > 300:  # 5 minutes timeout
                raise TimeoutError("OpenAI processing timed out after 5 minutes")
            time.sleep(2)

        if run_status.status != "completed":
            raise Exception(f"OpenAI run failed: {run_status.status}. Last error: {run_status.last_error}")

        # Fetch result
        messages = client.beta.threads.messages.list(thread_id=thread.id, order="desc")  # Get latest messages first
        output = None
        # Iterate through messages to find the assistant's latest response with text content
        for message in messages.data:
            if message.role == "assistant":
                for content_block in message.content:
                    if content_block.type == "text":
                        output = content_block.text.value
                        break
            if output:
                break

        if not output:
            raise Exception("No text content found in assistant's response.")

        return output

    except Exception as e:
        logger.error(f"Error during HTML extraction: {e}")
        # Log more details if available from OpenAI's error response
        if isinstance(e, openai.APIStatusError) and hasattr(e, 'response'):
            logger.error(f"OpenAI API Error Response: {e.response}")
        raise
    finally:
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
            except Exception:
                logger.warning(f"Could not remove temp file: {temp_file_path}")


def handler(event, context):
    logger.info(f"Received event: {json.dumps(event)}")
    try:
        body = json.loads(event.get("body", "{}"))
        fileData = body.get("html")
        if not fileData:
            return respond(400, "Missing HTML content")
        html = base64.b64decode(fileData)
        result = extract_product_info_from_html(html)
        return respond(200, result)
    except Exception as e:
        logger.error(traceback.format_exc())
        return respond(500, f"Internal Server Error: {str(e)}")


def respond(status_code, message):
    return {
        "statusCode": status_code,
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps({"message": message})
    }


if __name__ == "__main__":



    with open("testDom.txt", "rb") as f:
        encoded = base64.b64encode(f.read())
        decoded = encoded.decode()
    test_payload = {
        "html": decoded
    }
    response = handler({"body": json.dumps(test_payload)}, None)
    print(response)