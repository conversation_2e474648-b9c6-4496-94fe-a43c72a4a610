import boto3
import json
import logging
import os
from typing import Optional, Tuple

# Configure logging
logger = logging.getLogger(__name__)

# S3 bucket for caching results
S3_BUCKET_NAME = os.environ.get("RESULTS_S3_BUCKET", "pim-v3-sandbox-us-east-1-files")
s3_client = None

def get_s3_client():
    """Get or create S3 client instance."""
    global s3_client
    if s3_client is None:
        logger.info("Initializing S3 client")
        s3_client = boto3.client('s3')
    return s3_client

def get_s3_key_for_sku(sku: str) -> str:
    """
    Generate S3 key for a specific SKU.
    
    Args:
        sku: Product SKU
        
    Returns:
        S3 key string
    """
    return f"ai-demo-petmeds-product-enricher/{sku}.json"

def save_result_to_s3(sku: str, result: dict) -> bool:
    """
    Save result to S3 bucket.
    
    Args:
        sku: Product SKU
        result: Result dictionary to save
        
    Returns:
        Boolean indicating success
    """
    try:
        s3_key = get_s3_key_for_sku(sku)
        logger.info(f"Saving result for SKU {sku} to S3 at {s3_key}")
        
        s3 = get_s3_client()
        s3.put_object(
            Bucket=S3_BUCKET_NAME,
            Key=s3_key,
            Body=json.dumps(result),
            ContentType='application/json'
        )
        logger.info(f"Successfully saved result for SKU {sku} to S3")
        return True
    except Exception as e:
        logger.error(f"Failed to save result to S3: {str(e)}")
        return False

def get_result_from_s3(sku: str) -> Tuple[bool, Optional[dict]]:
    """
    Retrieve result from S3 bucket if it exists.
    
    Args:
        sku: Product SKU
        
    Returns:
        Tuple of (success_flag, result_dict)
    """
    try:
        s3_key = get_s3_key_for_sku(sku)
        logger.info(f"Checking for cached result for SKU {sku} at {s3_key}")
        
        s3 = get_s3_client()
        response = s3.get_object(Bucket=S3_BUCKET_NAME, Key=s3_key)
        result = json.loads(response['Body'].read().decode('utf-8'))
        
        logger.info(f"Found cached result for SKU {sku}")
        return True, result
    except s3_client.exceptions.NoSuchKey:
        logger.info(f"No cached result found for SKU {sku}")
        return False, None
    except Exception as e:
        logger.error(f"Error retrieving result from S3: {str(e)}")
        return False, None