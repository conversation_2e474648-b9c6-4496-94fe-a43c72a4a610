import requests
import base64
import json
import os
import logging
from typing import Optional, Dict, Any
from openai import OpenAI

from image_attribute_extractor_lambda_hackathon.rate_limiter import is_rate_limited, increment_count

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global client variable - will be initialized when needed
client = None

def get_openai_client():
    """Get or create OpenAI client instance."""
    global client
    if client is None:
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        client = OpenAI(api_key=api_key)
    return client


def load_image_from_url(image_url: str) -> str:
    """Download an image from a URL and convert to base64.

    Args:
        image_url: The URL of the image to download

    Returns:
        Base64 encoded string of the image

    Raises:
        requests.RequestException: If the image cannot be downloaded
        ValueError: If the URL is invalid
    """
    if not image_url or not image_url.startswith(('http://', 'https://')):
        raise ValueError(f"Invalid image URL: {image_url}")

    logger.info(f"Downloading image from URL: {image_url}")

    try:
        response = requests.get(image_url, timeout=30)
        response.raise_for_status()
        image_data = response.content

        if len(image_data) == 0:
            raise ValueError("Downloaded image is empty")

        logger.info(f"Successfully downloaded image ({len(image_data)} bytes)")
        return base64.b64encode(image_data).decode('utf-8')

    except requests.RequestException as e:
        logger.error(f"Failed to download image from {image_url}: {e}")
        raise

def create_comparative_prompt(existing_title: str, existing_description: str, existing_attributes: Dict[str, Any]) -> str:
    """
    Create a prompt for comparative analysis between existing and image-derived data.
    """
    return f"""
You are an expert AI assistant for analyzing product images and comparing them with existing product data.
Your task is to extract information from the product image and compare it with the existing data provided.

Existing Product Data:
Title: {existing_title}
Description: {existing_description}
Attributes: {json.dumps(existing_attributes, indent=2)}

Analyze the product image and for each field (title, description, and attributes):
1. Extract what you can see from the image
2. Compare it with the existing data
3. Determine if the existing data should be updated, enhanced, or if new information should be added
4. Provide reasoning for your decisions

For the title:
- Generate an improved/enhanced title based on what you see in the image
- Consider if the existing title accurately represents the product

For the description:
- Generate an enhanced description based on visual details from the image
- Include specific visual elements, materials, colors, design features you can observe

For attributes, extract and compare:
- product_type, brand, color, material, size, dimensions, shape, design_features, condition
- style, pattern, texture, closure_type, fit, season, gender, age_group
- Any text visible on the product (labels, logos, tags)
- Technical specifications if visible

Return ONLY valid JSON in this exact format:
{{
  "title": {{
    "current_value": "<existing value>",
    "generated_value": "<enhanced title based on image>",
    "reasoning": "<brief explanation of how you generated this>",
    "status": "<existing|new|updated>"
  }},
  "description": {{
    "current_value": "<existing value>",
    "generated_value": "<enhanced description based on image>",
    "reasoning": "<brief explanation of how you generated this>",
    "status": "<existing|new|updated>"
  }},
  "attributes": {{
    "<attribute_name>": {{
      "current_value": "<existing value>",
      "generated_value": "<value from image>",
      "reasoning": "<brief explanation>",
      "status": "<existing|new|updated>"
    }}
  }}
}}

Return ONLY valid JSON with no extra text or explanation. DONT RENAME the ATTRIBUTE NAMES. Add new attributes if needed. If there is no change in value, you can ignore the reasoning.
"""

def enhance_product_data_with_image_desc_attributes(image_url: str, existing_title: str, existing_description: str,
                                    existing_attributes: Dict[str, Any]) -> Dict[str, Any]:
    """
    Compare image-derived attributes with existing product data.

    Args:
        image_url: URL of the product image to analyze
        existing_title: Existing product title
        existing_description: Existing product description
        existing_attributes: Dictionary of existing product attributes

    Returns:
        Dictionary containing comparative analysis results

    Raises:
        ValueError: If no image URL is provided or if the API response is invalid
        Exception: If the API call fails
    """
    if image_url is None:
        raise ValueError("Image URL is required")

    try:
        # Build the comparative prompt
        prompt_text = create_comparative_prompt(existing_title, existing_description, existing_attributes)

        # Prepare content list for the user message
        user_content = [{"type": "text", "text": prompt_text}]

        # Add image content
        image_content = {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{load_image_from_url(image_url)}"
            }
        }
        user_content.append(image_content)

        logger.info("Calling OpenAI API for comparative image analysis")

        # Call GPT-4o with vision support
        openai_client = get_openai_client()
        completion = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful assistant that analyzes product images and compares them with existing data. Always return valid JSON."
                },
                {
                    "role": "user",
                    "content": user_content
                }
            ],
            max_tokens=2000,
            temperature=0.2
        )

        response_content = completion.choices[0].message.content
        logger.info("Received response from OpenAI API")

        # Validate and parse JSON response
        try:
            parsed_response = json.loads(response_content)
            return parsed_response
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Raw response: {response_content}")
            # Try to extract JSON from response if it contains extra text
            import re
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                try:
                    parsed_response = json.loads(json_match.group())
                    return parsed_response
                except json.JSONDecodeError:
                    pass

            raise ValueError(f"API returned invalid JSON: {response_content}")

    except Exception as e:
        logger.error(f"Error in compare_image_with_existing_data: {e}")
        raise

def handler(event, context):
    body = json.loads(event.get("body", "{}"))
    image_url =  body.get("image")
    description =  body.get("description")
    title =  body.get("title")
    attributes = body.get('attributes')
    host =  body.get("host")
    print(image_url, description, title, attributes)

    try:
        if host:
            limited, current, limit = is_rate_limited(host)
            if limited:
                return {
                    'statusCode': 429,
                    'body': json.dumps(f"Rate limit exceeded for host: {host}. Limit: {limit}")
                }

        # Extract attributes with product description
        result = enhance_product_data_with_image_desc_attributes(image_url,title,description,attributes)
        print("Extracted attributes:")
        print(json.dumps(result, indent=2))

        if host:
            increment_count(host)

        return {
            "statusCode": 200,
            "body": result
        }

    except Exception as e:
        logger.error(f"Failed to extract attributes: {e}")
        print(f"Error: {e}")


# Example usage
if __name__ == "__main__":
    # Example image URLs for testing
    test_urls = [
        "https://www.superkicks.in/cdn/shop/files/4_0412d651-8ed0-4653-8f1b-308222b0bf04.jpg"
    ]

    # Use the last URL for this example
    image_url = test_urls[-1]
    title = "ZOOM VOMERO 5"

    description = """
    Carve a new lane for yourself in the Zoom Vomero 5—your go-to for complexity, depth and easy styling. The richly layered design includes textiles, leather and plastic accents that come together to make one of the coolest sneakers of the season.
Upper mixes real and synthetic leather for a layered look built to last.
Mesh panels and ventilation ports on the heel keep it light and breathable.
Snappy and responsive, Zoom Air cushioning helps provide a quick-off-the-ground sensation.
Plastic caging on the side creates a supportive feel.
Rubber outsole gives you durable traction.
    """
    attributes = {
        "Manufacturer": "Nike",
"Country of Origin" : "Vietnam",
"Imported By" : "Nike India Pvt. Ltd.",
"Weight" : "1.10 KG",
"Generic Name" : "Shoes",
"Unit of Measurement" : "1 Pair",
"Marketed By" : "Superkicks India Pvt. Ltd.",
"Article Code" : "FJ4151-101"
    }

    try:
        # Extract attributes with product description
        result = enhance_product_data_with_image_desc_attributes(image_url, title, description, attributes)
        print("Extracted attributes:")
        print(json.dumps(result, indent=2))

    except Exception as e:
        logger.error(f"Failed to extract attributes: {e}")
        print(f"Error: {e}")



