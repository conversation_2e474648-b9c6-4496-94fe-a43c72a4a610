golden_standard_attributes = """
{
  "attributes": [
    {
      "attribute_name": "Brand",
      "values": [
        "Blue Buffalo",
        "Hill's Science Diet",
        "Purina Pro Plan",
        "Royal Canin",
        "Taste of the Wild",
        "Authority",
        "Simple Nourish",
        "Pedigree",
        "American Journey",
        "Merrick",
        "Wellness",
        "Orijen",
        "Acana",
        "Iams",
        "WholeHearted",
        "Instinct",
        "Fromm",
        "Diamond Naturals",
        "Nature's Recipe",
        "Eukanuba",
        "Iams",
        "Ra<PERSON>el <PERSON>",
        "Nutro",
        "Canidae"
      ]
    },
    {
      "attribute_name": "Lifestage",
      "values": [
        "<PERSON><PERSON>py",
        "Adult",
        "Senior",
        "All Life Stages"
      ]
    },
    {
      "attribute_name": "Breed Size",
      "values": [
        "Small Breeds",
        "Medium Breeds",
        "Large Breeds",
        "Giant Breeds",
        "All Breeds"
      ]
    },
    {
      "attribute_name": "Special Diet",
      "values": [
        "Grain-Free",
        "Gluten Free",
        "High-Protein",
        "Limited Ingredient Diet",
        "Low-Fat",
        "No Corn No Wheat No Soy",
        "Organic",
        "Natural",
        "Pea-Free",
        "Prescription",
        "Raw Coated",
        "Raw",
        "Sensitive Digestion",
        "Sensitive Stomach",
        "Human-Grade",
        "Vegan",
        "Hypoallergenic",
        "Freeze-Dried",
        "Vegetarian",
        "Weight Control"
      ]
    },
    {
      "attribute_name": "Health Feature",
      "values": [
        "Aging Care",
        "Allergy Care",
        "Brain Health",
        "Coat & Skin Care",
        "Dental & Tartar Care",
        "Digestive Health",
        "Heart Care",
        "Hip & Joint Support",
        "Immune Support",
        "Muscle Care",
        "Stress & Anxiety Relief",
        "Urinary Tract Health"
      ]
    },
    {
      "attribute_name": "Flavor",
      "values": [
        "Chicken",
        "Beef",
        "Lamb",
        "Fish",
        "Salmon",
        "Turkey",
        "Duck",
        "Pork",
        "Bison",
        "Sweet Potato",
        "Multi-Protein",
        "Venison"
      ]
    },
    {
      "attribute_name": "Packaging Type",
      "values": [
        "Bag",
        "Box"
      ]
    },
    {
      "attribute_name": "Food Form",
      "values": [
        "Dry Food",
        "Dehydrated",
        "Freeze-Dried",
        "Air-Dried",
        "Raw Coated",
        "Kibble"
      ]
    },
    {
      "attribute_name": "Grain Content",
      "values": [
        "Grain-Free",
        "Ancient Grains",
        "Brown Rice",
        "Oatmeal",
        "Quinoa",
        "Barley"
      ]
    },
    {
      "attribute_name": "Activity Level",
      "values": [
        "Active",
        "Working Dog",
        "Performance",
        "Moderate Activity",
        "Low Activity"
      ]
    },
    {
      "attribute_name": "Title"
    },
    {
      "attribute_name": "Description"
    },
    {
      "attribute_name": "Product Name"
    },
    {
      "attribute_name": "Size / Weight",
      "values": [
        "5-lb bag",
        "6-lb bag",
        "14-lb bag",
        "15-lb bag",
        "25-lb bag",
        "30-lb bag",
        "34-lb bag",
        "38-lb bag"
      ]
    },
    {
      "attribute_name": "Key Benefits"
    },
    {
      "attribute_name": "SKU"
    },
    {
      "attribute_name": "Ingredients"
    },
    {
      "attribute_name": "Caloric Content"
    },
    {
      "attribute_name": "Country of Origin"
    },
    {
      "attribute_name": "Caloric Content (kcal/cup)"
    },
    {
      "attribute_name": "Manufacturer"
    },
    {
      "attribute_name": "Storage Instructions"
    },
    {
      "attribute_name": "Expiration Date"
    },
    {
      "attribute_name": "Nutrional Adequacy Statement"
    },
    {
      "attribute_name": "Product Dimensions"
    },
    {
      "attribute_name": "UPC/Barcode"
    },
    {
      "attribute_name": "Guaranteed Analysis"
    },
    {
      "attribute_name": "Feeding Instructions"
    },
    {
      "attribute_name": "Transition Instructions"
    }
  ]
}
"""
golden_standard_seo_keyword="""[
        {
            "keyword": "dog food",
            "volume": 135000
        },
        {
            "keyword": "cat food",
            "volume": 90500
        },
        {
            "keyword": "blue buffalo dog food",
            "volume": 60500
        },
        {
            "keyword": "purina",
            "volume": 49500
        },
        {
            "keyword": "royal canin",
            "volume": 49500
        },
        {
            "keyword": "royal canin dog food",
            "volume": 49500
        },
        {
            "keyword": "best dog food",
            "volume": 40500
        },
        {
            "keyword": "fromm dog food",
            "volume": 40500
        },
        {
            "keyword": "taste of the wild dog food",
            "volume": 33100
        },
        {
            "keyword": "open farm dog food",
            "volume": 33100
        },
        {
            "keyword": "purina dog food",
            "volume": 33100
        },
        {
            "keyword": "diamond naturals dog food",
            "volume": 33100
        },
        {
            "keyword": "best dog food brands",
            "volume": 22200
        },
        {
            "keyword": "best puppy food",
            "volume": 18100
        },
        {
            "keyword": "raw dog food",
            "volume": 18100
        },
        {
            "keyword": "best dry dog food",
            "volume": 14800
        },
        {
            "keyword": "taste of the wild",
            "volume": 12100
        },
        {
            "keyword": "natural balance dog food",
            "volume": 12100
        },
        {
            "keyword": "dry cat food",
            "volume": 12100
        },
        {
            "keyword": "honest kitchen dog food",
            "volume": 12100
        },
        {
            "keyword": "senior dog food",
            "volume": 9900
        },
        {
            "keyword": "honest kitchen",
            "volume": 9900
        },
        {
            "keyword": "canidae",
            "volume": 9900
        },
        {
            "keyword": "canidae dog food",
            "volume": 9900
        },
        {
            "keyword": "dry dog food",
            "volume": 8100
        },
        {
            "keyword": "high protein dog food",
            "volume": 8100
        },
        {
            "keyword": "natural dog food",
            "volume": 8100
        },
        {
            "keyword": "healthy dog food",
            "volume": 8100
        },
        {
            "keyword": "limited ingredient dog food",
            "volume": 6600
        },
        {
            "keyword": "orijen",
            "volume": 6600
        },
        {
            "keyword": "large breed puppy food",
            "volume": 6600
        },
        {
            "keyword": "best dog foods",
            "volume": 6600
        },
        {
            "keyword": "healthiest dog food",
            "volume": 6600
        },
        {
            "keyword": "natural balance",
            "volume": 5400
        },
        {
            "keyword": "diamond naturals",
            "volume": 5400
        },
        {
            "keyword": "good dog food brands",
            "volume": 5400
        },
        {
            "keyword": "petco dog food",
            "volume": 5400
        },
        {
            "keyword": "dehydrated dog food",
            "volume": 3600
        },
        {
            "keyword": "best natural dog food",
            "volume": 3600
        },
        {
            "keyword": "puppy dog food",
            "volume": 3600
        },
        {
            "keyword": "all natural dog food",
            "volume": 3600
        },
        {
            "keyword": "best large breed puppy food",
            "volume": 3600
        },
        {
            "keyword": "weight management dog food",
            "volume": 2900
        },
        {
            "keyword": "natural balance limited ingredient",
            "volume": 2900
        },
        {
            "keyword": "healthy dog foods",
            "volume": 2900
        },
        {
            "keyword": "is grain free bad for dogs",
            "volume": 2900
        },
        {
            "keyword": "weight loss dog food",
            "volume": 2900
        },
        {
            "keyword": "diet dog food",
            "volume": 2400
        },
        {
            "keyword": "top dog food brands",
            "volume": 2400
        },
        {
            "keyword": "best dry dog foods",
            "volume": 2400
        },
        {
            "keyword": "nature dog food",
            "volume": 2400
        },
        {
            "keyword": "best dog food brand",
            "volume": 2400
        },
        {
            "keyword": "raw dog food recipes",
            "volume": 2400
        },
        {
            "keyword": "kibble dog food",
            "volume": 1900
        },
        {
            "keyword": "best dog kibble",
            "volume": 1900
        },
        {
            "keyword": "best brand dog food",
            "volume": 1900
        },
        {
            "keyword": "healthy dog food brands",
            "volume": 1900
        },
        {
            "keyword": "organic dog food brands",
            "volume": 1900
        },
        {
            "keyword": "dry dog food brands",
            "volume": 1900
        },
        {
            "keyword": "kibble dog food",
            "volume": 1900
        },
        {
            "keyword": "weight control dog food",
            "volume": 1900
        },
        {
            "keyword": "purina weight management dog food",
            "volume": 1600
        },
        {
            "keyword": "grain-free dog food",
            "volume": 1300
        },
        {
            "keyword": "purina dry dog food",
            "volume": 1300
        },
        {
            "keyword": "best small breed dog food",
            "volume": 1300
        },
        {
            "keyword": "canidae all life stages",
            "volume": 1300
        },
        {
            "keyword": "healthy weight dog food",
            "volume": 1300
        },
        {
            "keyword": "healthy food for dogs",
            "volume": 1300
        },
        {
            "keyword": "all life stages dog food",
            "volume": 1000
        },
        {
            "keyword": "best healthy dog food",
            "volume": 1000
        },
        {
            "keyword": "healthiest dog food brands",
            "volume": 1000
        },
        {
            "keyword": "dog food kibble",
            "volume": 880
        },
        {
            "keyword": "dry food for dogs",
            "volume": 880
        },
        {
            "keyword": "natural food for dogs",
            "volume": 880
        },
        {
            "keyword": "dog dry food",
            "volume": 720
        },
        {
            "keyword": "natural balance limited ingredient dog food",
            "volume": 720
        },
        {
            "keyword": "royal canin dry dog food",
            "volume": 720
        },
        {
            "keyword": "royal canin adult dog food",
            "volume": 720
        },
        {
            "keyword": "pet food brand",
            "volume": 720
        },
        {
            "keyword": "grain free dog food heart disease",
            "volume": 720
        },
        {
            "keyword": "giant breed dog food",
            "volume": 720
        },
        {
            "keyword": "best dog food for small breed dogs",
            "volume": 590
        },
        {
            "keyword": "natural dog food recipes",
            "volume": 590
        },
        {
            "keyword": "puppy dry food",
            "volume": 480
        },
        {
            "keyword": "dog food with high protein",
            "volume": 480
        },
        {
            "keyword": "what is the most healthy dog food",
            "volume": 480
        },
        {
            "keyword": "complete and balanced dog food",
            "volume": 390
        },
        {
            "keyword": "best dog food kibble",
            "volume": 390
        },
        {
            "keyword": "kibble for dogs",
            "volume": 390
        },
        {
            "keyword": "best puppy dry food",
            "volume": 390
        },
        {
            "keyword": "dehydrated raw dog food",
            "volume": 390
        },
        {
            "keyword": "healthy food for dog",
            "volume": 390
        },
        {
            "keyword": "best dried dog food",
            "volume": 390
        },
        {
            "keyword": "best senior dry dog food",
            "volume": 390
        },
        {
            "keyword": "high protein food for dogs",
            "volume": 320
        },
        {
            "keyword": "limited ingredient dog foods",
            "volume": 320
        },
        {
            "keyword": "grain free dog food good or bad",
            "volume": 320
        },
        {
            "keyword": "nutritious dog food",
            "volume": 320
        },
        {
            "keyword": "best senior dog food dry",
            "volume": 320
        },
        {
            "keyword": "adult dry dog food",
            "volume": 260
        },
        {
            "keyword": "small breed dry dog food",
            "volume": 260
        },
        {
            "keyword": "best kibble",
            "volume": 260
        },
        {
            "keyword": "good brands of dog food",
            "volume": 260
        },
        {
            "keyword": "california natural dog food",
            "volume": 260
        },
        {
            "keyword": "dog food brands list",
            "volume": 260
        },
        {
            "keyword": "canadian dog food brands",
            "volume": 260
        },
        {
            "keyword": "dog kibble brands",
            "volume": 260
        },
        {
            "keyword": "purina high protein",
            "volume": 210
        },
        {
            "keyword": "good dog food brand",
            "volume": 210
        },
        {
            "keyword": "all natural dog food brands",
            "volume": 210
        },
        {
            "keyword": "limited ingredient dog food natural balance",
            "volume": 210
        },
        {
            "keyword": "weight management food for dogs",
            "volume": 210
        },
        {
            "keyword": "nutro weight management dog food",
            "volume": 210
        },
        {
            "keyword": "limited ingredient dry dog food",
            "volume": 170
        },
        {
            "keyword": "purina weight management",
            "volume": 170
        },
        {
            "keyword": "natural dog food company",
            "volume": 170
        },
        {
            "keyword": "grain free dog food bad",
            "volume": 170
        },
        {
            "keyword": "grain free dog food warning",
            "volume": 170
        },
        {
            "keyword": "what is kibble dog food",
            "volume": 170
        },
        {
            "keyword": "premium dry dog food",
            "volume": 140
        },
        {
            "keyword": "brand of dog food",
            "volume": 140
        },
        {
            "keyword": "best weight management food for dogs",
            "volume": 140
        },
        {
            "keyword": "large breed dry dog food",
            "volume": 110
        },
        {
            "keyword": "best small breed dry dog food",
            "volume": 110
        },
        {
            "keyword": "limited ingredient diet dog food",
            "volume": 110
        },
        {
            "keyword": "worst dry dog food",
            "volume": 90
        },
        {
            "keyword": "best dry dog food reviews",
            "volume": 70
        },
        {
            "keyword": "dry dog kibble",
            "volume": 50
        },
        {
            "keyword": "best dehydrated raw dog food",
            "volume": 50
        },
        {
            "keyword": "dog foods brands",
            "volume": 50
        },
        {
            "keyword": "dry dog food online",
            "volume": 50
        },
        {
            "keyword": "dog kibble recipe",
            "volume": 50
        },
        {
            "keyword": "new dog food brands",
            "volume": 40
        },
        {
            "keyword": "canidae dog food ingredients",
            "volume": 40
        },
        {
            "keyword": "limited ingredient dog food petsmart",
            "volume": 30
        },
        {
            "keyword": "healthy dog food reviews",
            "volume": 30
        },
        {
            "keyword": "high protein dog food for small dogs",
            "volume": 30
        },
        {
            "keyword": "all life stages dog food reviews",
            "volume": 30
        },
        {
            "keyword": "puppy dry food grain free",
            "volume": 30
        },
        {
            "keyword": "purina weight management dog",
            "volume": 20
        },
        {
            "keyword": "best dog food comparison",
            "volume": 20
        },
        {
            "keyword": "best small breed dog food 2018",
            "volume": 20
        },
        {
            "keyword": "dehydrated dog food vs kibble",
            "volume": 20
        },
        {
            "keyword": "dehydrated raw dog food brands",
            "volume": 20
        },
        {
            "keyword": "dehydrated raw dog food reviews",
            "volume": 20
        },
        {
            "keyword": "best dry pet food",
            "volume": 20
        },
        {
            "keyword": "super premium dog food brands",
            "volume": 20
        },
        {
            "keyword": "high protein dog food problems",
            "volume": 20
        },
        {
            "keyword": "dehydrated dog food vs raw",
            "volume": 20
        },
        {
            "keyword": "best high protein dog food brands",
            "volume": 20
        },
        {
            "keyword": "list of dog foods that meet aafco standards",
            "volume": 20
        },
        {
            "keyword": "high protein dog food comparison",
            "volume": 20
        },
        {
            "keyword": "canidae life stages",
            "volume": 20
        },
        {
            "keyword": "life stages dog food",
            "volume": 20
        },
        {
            "keyword": "dog kibble vs raw food",
            "volume": 20
        },
        {
            "keyword": "large kibble dry dog food",
            "volume": 20
        },
        {
            "keyword": "puppy dry food reviews",
            "volume": 20
        },
        {
            "keyword": "what are the 10 best dog foods?",
            "volume": 20
        },
        {
            "keyword": "dog food kibble ratings",
            "volume": 20
        },
        {
            "keyword": "why is my dog not eating his food anymore?",
            "volume": 20
        },
        {
            "keyword": "how much is blue wilderness dog food",
            "volume": 20
        },
        {
            "keyword": "blue buffalo salmon and potato dog food",
            "volume": 20
        },
        {
            "keyword": "best small breed dog food 2017",
            "volume": 10
        },
        {
            "keyword": "natural balance limited ingredient dog food sweet potato and fish",
            "volume": 10
        },
        {
            "keyword": "senior light dry dog food",
            "volume": 10
        },
        {
            "keyword": "senior dry food",
            "volume": null
        },
        {
            "keyword": "healthy dog foods list",
            "volume": null
        },
        {
            "keyword": "types of dog food brand",
            "volume": null
        },
        {
            "keyword": "blue buffalo freedom grain free dry puppy food",
            "volume": null
        },
        {
            "keyword": "blue buffalo wilderness salmon vs duck",
            "volume": null
        },
        {
            "keyword": "blue buffalo wilderness salmon reviews",
            "volume": null
        },
        {
            "keyword": "blue buffalo wilderness salmon cat food review",
            "volume": null
        },
        {
            "keyword": "best senior dry dog foods",
            "volume": null
        }
    ]
"""
pim_attribute_filters = """
🔍 Attribute Filters (Apply only if matching):
[{
  "attributeDetails": [
    {
      "type": "TEXT",
      "name": "Sku",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "EAN",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Tagline",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "UPC",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Headline",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 1 Heading",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 1 Info",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product SKU 1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 1 SKU 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 2 Heading",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 2 Info",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 2 SKU 1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 2 SKU 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 3 Heading",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 3 Info",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 3 SKU 1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 3 SKU 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 4 Heading",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 4 Info",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 4 SKU 1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 4 SKU 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Sub Headline",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "App Reminder",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Autoship Option",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Auxiliary Media",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Backorder Message",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Backorder Date",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Banned Products",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Bought Together Products",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Brand",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Bazaarvoice Average Rating",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Bazaarvoice Rating Range",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Bazaarvoice Review Count",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Confirmation Banner Asset ID",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Controlled States",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Manufacturer",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Disallow as recommendation",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Discontinued Upsell 1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Discontinued Upsell 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Discontinued Upsell 3",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Do not ship to countries",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Expert Rating",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Fixed Related Products",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Flavor",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Frequently Bought Together Override",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Generic Alternative Title",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Generic Equivalent Product ID",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Generic Name",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Generics Page Text",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Group",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "How to use tab rwd",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ingredients Filter",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ingredients",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ingredients Tab Name",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ingredients Tab Rwd",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "JW Player Video ID",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "JW Player Video ID 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "JW Player Video ID 3",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "JW Player Video ID 4",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "JW Player Video ID 5",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Label Postfix",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Life",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Localized Tax Class",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Product Details",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Manufacturer Product ID",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Max Weight",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Milligrams",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Minimum Weight",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "MPN",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Name",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "No Tax in States",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Page Description",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Page Keywords",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Page Title",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Page URL",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "PCRX Part Number",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "PDP Shipping Message",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Pill Count",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Pill Count Variation",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "PageKeywords",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Private Label",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Product Info Tab Rwd",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Promo Icon Image",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Promo Icon Start Date",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Promo Icon Position",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Recommended Skus",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Restricted Shipping States",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "RWD Alternate",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "RWD Generic",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Searchable If Unavailable",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ship Height",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ship Length",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Shipping Notification",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ship Weight",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ship Width",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Description",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "SKU Color",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Sku Flavor",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "SKU Size",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "SKU Weight",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Sort Attribute",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Species",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Store Tax Class",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Supplier",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Supplier Product ID",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Tax Class",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Rendering Template",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Title Tag",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Unit",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Unit Measure",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Upsell",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Usage Image URL",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Weight Shared Variation",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Workato XML Export Date",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "parentSku",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "preview_url",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Main Image",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Active",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Default SKU",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Brief Selling Point",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Classification Category",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Primary Category",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Variation Attribute",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Acute/Chronic",
      "validation": {
        "acceptedValues": [
          "acute",
          "chronic",
          "chronic-acute"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Administration Form",
      "validation": {
        "acceptedValues": [
          "injection",
          "irrigation",
          "oral",
          "otic"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Age Range",
      "validation": {
        "acceptedValues": [
          "0 - 2 Years",
          "3 - 5 Years",
          "6 - 8 Years",
          "9 - 12 Years"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Age Requirement",
      "validation": {
        "acceptedValues": [
          "newborn+",
          "8-weeks+",
          "12-weeks+",
          "6-months+"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "ApplyTo",
      "validation": {
        "acceptedValues": [
          "face",
          "mouth",
          "eyes",
          "ears"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Bandage Width x Length",
      "validation": {
        "acceptedValues": [
          "2-in-W-5-yd-L",
          "3-in-W-5-yd-L",
          "4-in-W-5-yd-L",
          "n-a"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #3",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #4",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #5",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #6",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #7",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Catalog",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Category Codes (Rx)",
      "validation": {
        "acceptedValues": [
          "spot-treatment",
          "oral-treatment",
          "flea-tick-shampoo",
          "powder-spray-wipe"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Cautions",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Collar Type (OTC)",
      "validation": {
        "acceptedValues": [
          "herbal",
          "flea-and-tick"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Compound Flavor",
      "validation": {
        "acceptedValues": [
          "cherry",
          "chicken",
          "beef",
          "tuna"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Dental Features",
      "validation": {
        "acceptedValues": [
          "breath-freshening",
          "plaque-removal",
          "tartar-removal",
          "yeast-removal"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Directions",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Dosage for Cats",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Dosage for Dogs",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Dosage for Horses",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Drug Type",
      "validation": {
        "acceptedValues": [
          "adrenergic-agent",
          "alkalizer",
          "analgesic",
          "antiviral"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "FoodFormDetail",
      "validation": {
        "acceptedValues": [
          "air-dried",
          "birdseed",
          "bloodworms",
          "cracked-corn"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "FoodTexture",
      "validation": {
        "acceptedValues": [
          "aspic-gelee",
          "bits-in-broth",
          "chunks-in-gravy",
          "flaked"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "FoodTopCategory",
      "validation": {
        "acceptedValues": [
          "Food",
          "Treats",
          "Prescription_Food_Treats"
        ]
      }
    },
    {
      "type": "BOOLEAN",
      "name": "Frozen",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Gender Suitable",
      "validation": {
        "acceptedValues": [
          "female",
          "male",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Horse Size",
      "validation": {
        "acceptedValues": [
          "draft-horse"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "How does this product work?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "How should <Product Name> be given/administered?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "How should I store this product:",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Legal Qs (Prop65)",
      "validation": {
        "acceptedValues": [
          "No",
          "Yes - Cancer",
          "Yes - Reproductive Harm",
          "Yes - Cancer and Reproductive Harm"
        ]
      }
    },
    {
      "type": "BOOLEAN",
      "name": "Marketing Exclusion",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Materials/Ingredients table",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "National Drug Code",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Subbrand",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Pill/Each",
      "validation": {
        "acceptedValues": [
          "pill",
          "each"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Prescription_Food_Treats",
      "validation": {
        "acceptedValues": [
          "Prescription Dry Food",
          "Prescription Wet Food",
          "Prescription Biscuits & Crunchy Treats",
          "Prescription Soft & Chewy Treats"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Product Class-L4",
      "validation": {
        "acceptedValues": [
          "rx-none",
          "rx-flea-tick-heartworm",
          "rx-flea-tick",
          "rx-heartworm"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Product Class-Rx",
      "validation": {
        "acceptedValues": [
          "rx-other",
          "rx-nsaid",
          "rx-heart",
          "rx-derm",
          "rx-motion-sickness",
          "rx-infection",
          "rx-insulin",
          "rx-incontinence",
          "rx-cushings-disease",
          "rx-virus",
          "rx-medical-equipment",
          "rx-anxiety",
          "rx-behavior-issues",
          "rx-cancer",
          "rx-parasiticide",
          "rx-allergies",
          "rx-brain",
          "rx-digestive",
          "rx-eye-ear",
          "rx-recovery-dehydration",
          "rx-thyroid",
          "rx-vitamins"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Product Details (What is <Product Name>?)",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Product Weight",
      "validation": {
        "acceptedValues": [
          "Less-than-5",
          "5-10-lbs",
          "11-20-lbs",
          "21-30-lbs"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "ProductForm",
      "validation": {
        "acceptedValues": [
          "block",
          "capsule",
          "chew",
          "chewable-tablet",
          "crumble",
          "cleanser",
          "diffuser",
          "gel",
          "granules",
          "injectable",
          "liquid",
          "lotion",
          "ointment",
          "packing",
          "paste",
          "pellet",
          "pill",
          "powder",
          "puree",
          "rinse",
          "soft-chew",
          "softgel",
          "solution",
          "spray",
          "suspension",
          "tablet",
          "wafer",
          "wipes",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "ProductWeightOunces",
      "validation": {
        "acceptedValues": [
          "less-than-3",
          "3-5-oz",
          "6-9-oz",
          "10-12-oz",
          "12-oz-and-above"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Prop65 Attachment Verbiage",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "PurchaseUOM",
      "validation": {
        "acceptedValues": [
          "Each",
          "Case",
          "Master Case",
          "pack"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Pyrethrins",
      "validation": {
        "acceptedValues": [
          "Yes"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Pyrethrins Caution Statement",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Restricted Availability",
      "validation": {
        "acceptedValues": [
          "TRUE"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Rx & OTC Category",
      "validation": {
        "acceptedValues": [
          "Flea, Tick, Mite & Dewormers: Spot Treatments",
          "Flea, Tick, Mite & Dewormers: Oral Treatments",
          "Flea, Tick, Mite & Dewormers: Shampoos & Dips",
          "Flea, Tick, Mite & Dewormers: Powders, Sprays & Wipes"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Season (OTC)",
      "validation": {
        "acceptedValues": [
          "year-round",
          "spring-summer",
          "fall-winter"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Suitable For:",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Unit Type",
      "validation": {
        "acceptedValues": [
          "mL",
          "tablets"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "z-human-label",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What are the potential side effects of <Product Name>?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What happens if I miss giving a dose of <Product Name>?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What happens if I overdose my pet on <Product Name>?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What is the most important thing I should know about <Product Name>",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What other drugs will affect <Product Name>?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What should I avoid while giving <Product Name> to my pet?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What should I discuss with my veterinarian before giving <Product Name> to my pet?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What special precautions are there?",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Bird Type",
      "validation": {
        "acceptedValues": [
          "african-grey",
          "amazon",
          "budgie",
          "canary",
          "chicken",
          "cockatiel",
          "cockatoo",
          "conure",
          "dove-pigeon",
          "duck",
          "finch",
          "hookbill",
          "lovebird",
          "macaw",
          "parakeet",
          "parrot",
          "quaker",
          "wild-bird",
          "parrotlet",
          "bluebird",
          "bunting",
          "cardinal",
          "catbird",
          "chickadee",
          "chukar",
          "dove",
          "emu",
          "flicker",
          "gamebird",
          "geese",
          "grosbeak",
          "grouse",
          "guinea-fowl",
          "hummingbird",
          "jay",
          "junco",
          "kinglet",
          "kiwi",
          "mockingbird",
          "nuthatch",
          "oriole",
          "ostrich",
          "patridge",
          "pheasant",
          "poultry",
          "quail",
          "redpoll",
          "robin",
          "senegal",
          "siskin",
          "songbird",
          "sparrow",
          "squab",
          "starling",
          "swan",
          "tanager",
          "thrasher",
          "thrush",
          "titmouse",
          "towhee",
          "turkey",
          "vireo",
          "warbler",
          "waterfowl",
          "woodpecker",
          "wren",
          "toucan",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Chew Style",
      "validation": {
        "acceptedValues": [
          "moderate",
          "extreme",
          "tough",
          "Light"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Closure Type Diapers",
      "validation": {
        "acceptedValues": [
          "adhesive",
          "hook-and-loop",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Health Feature",
      "validation": {
        "acceptedValues": [
          "allergy-relief",
          "anti-fungal-anti-bacterial",
          "anti-parasitic",
          "appetite-stimulation",
          "brain-health",
          "calming",
          "circulatory-care",
          "colic-relief",
          "dander",
          "dental-and-breath-care",
          "diabetic-support",
          "digestive-health",
          "ear-care",
          "ear-mite-treatment",
          "eye-care",
          "first-aid",
          "hairball-control",
          "heart-care",
          "high-energy",
          "high-protein",
          "hip-and-joint-support",
          "hoof-care",
          "hormone-support",
          "immune-support",
          "itch-and-redness-remedy",
          "kidney-care",
          "liver-care",
          "milk-replacer",
          "muscle-care",
          "nerve-care",
          "paw-care",
          "pest-control",
          "recovery",
          "reproduction-and-nursing",
          "respiratory-care",
          "senior-care",
          "sensitive-digestion",
          "sensitive-skin",
          "shedding-control",
          "skin-and-coat-health",
          "sun-protection",
          "tear-stain-removal",
          "thyroid-support",
          "urinary-tract-health",
          "vitamins-and-minerals",
          "weight-management",
          "n-a",
          "metabolic",
          "electrolyte"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Intro Year",
      "validation": {
        "acceptedValues": [
          "2014",
          "2015",
          "2016",
          "2017",
          "2018",
          "2019",
          "2020",
          "2021",
          "2022",
          "2023",
          "2024",
          "2025",
          "2026",
          "2027"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Kill/Treats",
      "validation": {
        "acceptedValues": [
          "biting-flies",
          "chewing-lice",
          "ear-mites",
          "flea-eggs",
          "flea-larvae",
          "fleas",
          "heartworms",
          "hookworms",
          "mosquitoes",
          "roundworms",
          "tapeworms",
          "ticks",
          "whipworms",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Made In",
      "validation": {
        "acceptedValues": [
          "afghanistan",
          "albania",
          "algeria",
          "andorra",
          "angola",
          "antigua-and-barbuda",
          "argentina",
          "armenia",
          "australia",
          "austria",
          "azerbaijan",
          "the-bahamas",
          "bahrain",
          "bangladesh",
          "barbados",
          "belarus",
          "belgium",
          "belize",
          "benin",
          "bhutan",
          "bolivia",
          "bosnia-and-herzegovina",
          "botswana",
          "brazil",
          "brunei",
          "bulgaria",
          "burkina-faso",
          "burundi",
          "cabo-verde",
          "cambodia",
          "cameroon",
          "canada",
          "central-african-republic",
          "chad",
          "chile",
          "china",
          "colombia",
          "comoros",
          "democratic-republic-of-the-congo",
          "republic-of-the-congo",
          "costa-rica",
          "cÃ´te-dâ€™ivoire",
          "croatia",
          "cuba",
          "cyprus",
          "czech-republic",
          "denmark",
          "djibouti",
          "dominica",
          "dominican-republic",
          "east-timor-(timor-leste)",
          "ecuador",
          "egypt",
          "el-salvador",
          "equatorial-guinea",
          "eritrea",
          "estonia",
          "eswatini",
          "ethiopia",
          "fiji",
          "finland",
          "france",
          "gabon",
          "the-gambia",
          "georgia",
          "germany",
          "ghana",
          "greece"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Materials/Ingredients text",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "One Time Buy",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Recovery Product Type",
      "validation": {
        "acceptedValues": [
          "elizabethan-collar-e-collar",
          "soft-cone",
          "inflatable-cone",
          "suit",
          "boot"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Repels/Prevents",
      "validation": {
        "acceptedValues": [
          "biting-flies",
          "chewing-lice",
          "ear-mites",
          "flea-eggs",
          "flea-larvae",
          "fleas",
          "heartworms",
          "hookworms",
          "mosquitoes",
          "roundworms",
          "tapeworms",
          "ticks",
          "whipworms",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Sellable UOM",
      "validation": {
        "acceptedValues": [
          "each",
          "pill",
          "bottle",
          "case",
          "pack"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Sourced From",
      "validation": {
        "acceptedValues": [
          "afghanistan",
          "albania",
          "algeria",
          "andorra",
          "angola",
          "antigua-and-barbuda",
          "argentina",
          "armenia",
          "australia",
          "austria",
          "azerbaijan",
          "the-bahamas",
          "bahrain",
          "bangladesh",
          "barbados",
          "belarus",
          "belgium",
          "belize",
          "benin",
          "bhutan",
          "bolivia",
          "bosnia-and-herzegovina",
          "botswana",
          "brazil",
          "brunei",
          "bulgaria",
          "burkina-faso",
          "burundi",
          "cabo-verde",
          "cambodia",
          "cameroon",
          "canada",
          "central-african-republic",
          "chad",
          "chile",
          "china",
          "colombia",
          "comoros",
          "democratic-republic-of-the-congo",
          "republic-of-the-congo",
          "costa-rica",
          "cÃ´te-dâ€™ivoire",
          "croatia",
          "cuba",
          "cyprus",
          "czech-republic",
          "denmark",
          "djibouti",
          "dominica",
          "dominican-republic",
          "east-timor-(timor-leste)",
          "ecuador",
          "egypt",
          "el-salvador",
          "equatorial-guinea",
          "eritrea",
          "estonia",
          "eswatini",
          "ethiopia",
          "fiji",
          "finland",
          "france",
          "gabon",
          "the-gambia",
          "georgia",
          "germany",
          "ghana",
          "greece"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Test Type",
      "validation": {
        "acceptedValues": [
          "allergy-testing",
          "blood-glucose-monitoring",
          "dna-testing",
          "food-intolerance-testing",
          "leukemia-testing",
          "urine-testing",
          "worm-testing",
          "n-a"
        ]
      }
    },
    {
      "type": "NUMBER",
      "name": "Autoship Discount Percent",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Bazaarvoice Filter Rating",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Below the Fold",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Change Frequency",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Controlled Substance?",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Disable Order Autoship",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Discountable",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Do not split",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Drop Ship",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Expert Review Count",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Food Form",
      "validation": {
        "acceptedValues": [
          "Dry Food",
          "Wet Food",
          "Freeze Dried",
          "Dehydrated",
          "Frozen",
          "Dry Food Toppings",
          "Wet Food Toppings",
          "Other",
          "Health Specific Diets",
          "Vet Diet",
          "OTC"
        ]
      }
    },
    {
      "type": "BOOLEAN",
      "name": "Ground Only",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Has Map Price?",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Has Medication Reminder Option",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Health Condition Therapy",
      "validation": {
        "acceptedValues": [
          "Anxiety",
          "Arthritis",
          "Asthma",
          "Bacterial",
          "Behavior",
          "Bone / Joint",
          "Cancer",
          "Cough",
          "Dental",
          "Diabetes",
          "Digestive",
          "Eye",
          "Fleas",
          "Flies",
          "Glaucoma",
          "Hairball",
          "Heart / Blood Pressure",
          "Heartworm",
          "Hoof",
          "Hormonal / Endocrine",
          "Immunity",
          "Incontinence",
          "Itch",
          "Kidney",
          "Liver",
          "Mites",
          "Nail",
          "Nausea",
          "Odor",
          "Pain / Inflammation",
          "Respiratory",
          "Ringworm",
          "Seizure / Epilepsy",
          "Shedding",
          "Skin / Coat",
          "Thrush",
          "Thyroid",
          "Ticks",
          "Urinary",
          "Viral",
          "Weight",
          "Worms",
          "Wound",
          "Behavior Management",
          "Daily Maintenance",
          "Weight Maintenance",
          "Dental Care",
          "Dental & Breath Care",
          "Digestive Care",
          "Glucose Management",
          "Urinary Care",
          "Eye Health",
          "Skin Care",
          "Heart Health",
          "Kidney Care",
          "Liver Function",
          "Heartworm Prevention",
          "Joint Care",
          "Weight Management",
          "Liver Support",
          "Nutrition",
          "Senior Health",
          "Thyroid Support",
          "Hip/Joint",
          "Allergies",
          "Fungal Infections",
          "Ear Care",
          "Joint Pain",
          "Behavior Issues",
          "Constipation",
          "Cushings Disease",
          "Dehydration",
          "Digestive Issues",
          "Ear Infections",
          "Ear Mites",
          "Hair Loss",
          "High Blood Pressure",
          "Congestive Heart Failure",
          "Heartburn",
          "Hookworms",
          "Hypothyroidism",
          "Hyperthyroidism",
          "Hot Spots",
          "Inflammatory Bowel Disease",
          "Kennel Cough",
          "Kidney Disease",
          "Kidney Stones",
          "Liver Disease",
          "Mange",
          "Motion Sickness",
          "Muscle Spasms / Tremors",
          "N/A",
          "Unhealthy Weight",
          "Warts",
          "Vomiting",
          "Urinary Tract Infections",
          "Ulcers",
          "Tapeworms",
          "Skin Infections",
          "Senility",
          "Roundworms",
          "Respiratory Infections",
          "Recovery",
          "Parvovirus",
          "Eye Infections",
          "Allergy"
        ]
      }
    },
    {
      "type": "BOOLEAN",
      "name": "Hide Generic / Alternative Area",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Ice Pack",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "In Stock",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Life Stage",
      "validation": {
        "acceptedValues": [
          "all-lifestages",
          "adult",
          "baby",
          "nursing",
          "senior",
          "kitten",
          "puppy",
          "All Life Stages",
          "Large Breed",
          "Small Breed"
        ]
      }
    },
    {
      "type": "NUMBER",
      "name": "List Price",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "MAP Price",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Max Autoship Discount Amount",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Max Quantity",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Minimum Order Quantity",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Online",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Package Quantity",
      "validation": {
        "acceptedValues": [
          "1",
          "2",
          "3",
          "4",
          "6",
          "7",
          "12",
          "14",
          "15",
          "18",
          "24",
          "28",
          "30",
          "42",
          "60",
          "90",
          "100",
          "120",
          "180",
          "540",
          "Less-than-5",
          "5-10-count",
          "11-20-count",
          "21-30-count",
          "31-40-count",
          "41-and-above"
        ]
      }
    },
    {
      "type": "NUMBER",
      "name": "Promo Icon Expiry",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "RWD Alternate Equivalent",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "RWD Generic Equivalent",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Sales Volume",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Search Placement",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Search Rank",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Searchable",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Show Bundled Items",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Show Mfr Message",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Show Multi Pre Cart",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Show Real Price?",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Step Quantity",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Vet Authorized Product",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Sale Price",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Map Priority",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Pet Size",
      "validation": {
        "acceptedValues": [
          "allbreeds",
          "x-small",
          "small",
          "medium",
          "large",
          "giant",
          "X-Large",
          "All Breed Sizes"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Online From",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Online To",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Unit Quantity",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "z-generic",
      "validation": {
        "acceptedValues": [
          "TRUE"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Package Type",
      "validation": {
        "acceptedValues": [
          "Dry",
          "Wet",
          "N/A",
          "Freeze Dried",
          "Box"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Generic",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Pillform",
      "validation": {
        "acceptedValues": [
          "Liquid",
          "Tablet",
          "Caplet",
          "Capsule",
          "Chewable Tablet",
          "Soft Chew",
          "Injectable",
          "Powder",
          "Suspension",
          "Solution",
          "Transdermal"
        ]
      }
    },
    {
      "type": "NUMBER",
      "name": "Product Key Old",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Selling channel",
      "validation": {
        "acceptedValues": [
          "petmeds",
          "pcrx",
          "care"
        ]
      }
    },
    {
      "type": "NUMBER",
      "name": "Expiry Date",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "New Arrival Flag",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Discontinued Flag",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Syringe Size",
      "validation": {
        "acceptedValues": [
          "U-40",
          "U-60",
          "U-80"
        ]
      }
    },
    {
      "type": "BOOLEAN",
      "name": "Transition Out Flag",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Product Key",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Product Short Name",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Supplies",
      "validation": {
        "acceptedValues": [
          "Toys",
          "Beauty & Grooming",
          "Waste Management",
          "Cleaners & Stain Removers",
          "Bowls & Feeding",
          "Training & Behavior",
          "Beds, Crates, & Gear",
          "Test Kits",
          "Litter & Accessories",
          "Cat Furniture"
        ]
      }
    }
  ]
}]
"""
frequently_asked_questions = """
{
  "questions": [
    {
      "topic": "Nutrition & Ingredients",
      "topic_questions": [
        "What are the main protein sources in this dry dog food?",
        "Does it include whole grains, legumes, or potato?",
        "Are there any artificial colors, flavors, or preservatives?"
      ]
    },
    {
      "topic": "Feeding & Serving Guidelines",
      "topic_questions": [
        "How much should I feed my dog based on weight and activity level?",
        "Can I mix this dry kibble with wet food or toppers?",
        "How often should I adjust portion sizes as my dog ages?"
      ]
    },
    {
      "topic": "Health & Wellness Benefits",
      "topic_questions": [
        "How does this formula support healthy skin and coat?",
        "Does it include joint-support supplements like glucosamine?",
        "Can it help with weight management or digestive health?"
      ]
    },
    {
      "topic": "Allergies & Sensitivities",
      "topic_questions": [
        "Is this dry food grain-free or limited-ingredient?",
        "What common allergens (chicken, beef, dairy) are avoided?",
        "Is it suitable for dogs with sensitive stomachs?"
      ]
    },
    {
      "topic": "Special Diets & Life Stages",
      "topic_questions": [
        "Which recipes are formulated for puppies/seniors/large breeds?",
        "Do you offer veterinary-recommended or prescription options?",
        "Are there breed-specific or size-specific formulas?"
      ]
    },
    {
      "topic": "Storage, Freshness & Shelf Life",
      "topic_questions": [
        "How should I store this kibble to maintain freshness?",
        "What is the best-by or expiration date format?",
        "Can I freeze or refrigerate unused portions?"
      ]
    },
    {
      "topic": "Sourcing & Quality Assurance",
      "topic_questions": [
        "Where are the ingredients sourced from?",
        "Are there any BRC, SQF, or AAFCO certifications?",
        "How frequently do you conduct quality/control tests?"
      ]
    },
    {
      "topic": "Price, Packaging & Purchase Options",
      "topic_questions": [
        "What bag sizes are available and what’s the cost per pound?",
        "Do you offer subscription discounts or bulk-buy savings?",
        "Can I return or exchange if my dog doesn’t like it?"
      ]
    },
    {
      "topic": "Comparison & Alternatives",
      "topic_questions": [
        "How does this formula differ from your other chicken-based kibble?",
        "What makes it different from top competitor brands?",
        "Which recipe is best for active dogs vs. less active dogs?"
      ]
    },
    {
      "topic": "Sustainability & Ethical Practices",
      "topic_questions": [
        "Is the packaging recyclable or made from recycled materials?",
        "Do you use any by-products or sustainable protein sources?"
      ]
    }
  ]
}
"""
