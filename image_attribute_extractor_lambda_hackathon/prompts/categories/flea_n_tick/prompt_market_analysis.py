golden_standard_attributes= """
{
  "attributes": [
    {
      "attribute_name": "<PERSON>",
      "values": [
        "FRONTLIN<PERSON>",
        "NexGard",
        "Simparica",
        "K9 Advantix",
        "K9 Advantix ||",
        "Bravecto",
        "Seresto",
        "Advantage",
        "Advantage II",
        "Credelio",
        "Revolution",
        "Trifexis",
        "Comfortis",
        "Vectra 3D",
        "Hartz",
        "Sergeant's",
        "Virbac",
        "Elanco",
        "Merck",
        "Zoetis",
        "Bayer",
        "Capstar"
      ]
    },
    {
      "attribute_name": "Treatment Form",
      "values": [
        "Topical Treatment",
        "Oral Treatment",
        "Collar",
        "Shampoo & Spray",
        "Home & Yard Treatment",
        "Wipe",
        "Powder",
        "Dip",
        "<PERSON>ogger",
        "Mousse",
        "Spot On",
        "Sprays & Wipes"
      ]
    },
    {
      "attribute_name": "Lifestage",
      "values": [
        "<PERSON><PERSON><PERSON>",
        "Adult",
        "Senior",
        "All Ages"
      ]
    },
    {
      "attribute_name": "Pet Weight",
      "values": [
        "0-10 lbs",
        "4-10 lbs",
        "5-22 lbs",
        "11-20 lbs",
        "21-55 lbs",
        "23-44 lbs",
        "45-88 lbs",
        "56-99 lbs",
        "89-132 lbs",
        "100+ lbs"
      ]
    },
    {
      "attribute_name": "Pest Type",
      "values": [
        "Fleas",
        "Ticks",
        "Flea Eggs",
        "Flea Larvae",
        "Mosquitoes",
        "Lice",
        "Mites",
        "Chewing Lice"
      ]
    },
    {
      "attribute_name": "Prescription Required",
      "values": [
        "Yes",
        "No"
      ]
    },
    {
      "attribute_name": "Title"
    },
    {
      "attribute_name": "Description"
    },
    {
      "attribute_name": "SKU"
    },
    {
      "attribute_name": "Dosage Count",
      "values": [
        "1 Dose",
        "2 Dose",
        "3 Doses",
        "4 Doses",
        "6 Doses",
        "8 Doses"
      ]
    },
    {
      "attribute_name": "Key Benefits"
    },
    {
      "attribute_name": "Uses"
    },
    {
      "attribute_name": "Active Ingredients"
    },
    {
      "attribute_name": "Directions for Use"
    },
    {
      "attribute_name": "Storage Instructions"
    },
    {
      "attribute_name": "Manufacturer"
    },
    {
      "attribute_name": "Country of Origin"
    },
    {
      "attribute_name": "Product Dimensions"
    },
    {
      "attribute_name": "UPC/Barcode"
    },
    {
      "attribute_name": "Expiration Date"
    },
    {
      "attribute_name": "Safety Data Sheet"
    },
    {
      "attribute_name": "Veterinary Recommendation"
    },
    {
      "attribute_name": "Application Frequency"
    },
    {
      "attribute_name": "Onset of Action"
    },
    {
      "attribute_name": "Precautions"
    },
    {
      "attribute_name": "Drug Type"
    },
    {
      "attribute_name": "Possible Side Effects"
    }
  ]
}
"""
golden_standard_seo_keyword="""
[
    {
      "keyword": "petsmart",
      "volume": 3365689
    },
    {
      "keyword": "chewy",
      "volume": 1484050
    },
    {
      "keyword": "petco near me",
      "volume": 433473
    },
    {
      "keyword": "good rx",
      "volume": 432328
    },
    {
      "keyword": "dog food",
      "volume": 101879
    },
    {
      "keyword": "purina pro plan",
      "volume": 69706
    },
    {
      "keyword": "cat food",
      "volume": 59691
    },
    {
      "keyword": "hills",
      "volume": 45250
    },
    {
      "keyword": "royal canin",
      "volume": 34277
    },
    {
      "keyword": "royal canin dog food",
      "volume": 32888
    },
    {
      "keyword": "best dog food",
      "volume": 31564
    },
    {
      "keyword": "pet food",
      "volume": 25266
    },
    {
      "keyword": "homemade dog food",
      "volume": 17164
    },
    {
      "keyword": "royal canin cat food",
      "volume": 15822
    },
    {
      "keyword": "petco vet",
      "volume": 15738
    },
    {
      "keyword": "dog food recipes",
      "volume": 15043
    },
    {
      "keyword": "purina pro plan dog food",
      "volume": 12031
    },
    {
      "keyword": "dog food advisor",
      "volume": 12011
    },
    {
      "keyword": "grain free dog food",
      "volume": 11100
    },
    {
      "keyword": "hills dog food",
      "volume": 9050
    },
    {
      "keyword": "homemade dog food recipes vet approved",
      "volume": 7228
    },
    {
      "keyword": "hill's prescription diet",
      "volume": 6050
    },
    {
      "keyword": "petsmart dog food",
      "volume": 4640
    },
    {
      "keyword": "low cost dog vaccinations near me",
      "volume": 4106
    },
    {
      "keyword": "best dog foods on the market",
      "volume": 3801
    },
    {
      "keyword": "dog food near me",
      "volume": 2742
    },
    {
      "keyword": "hills prescription diet dog food",
      "volume": 2700
    },
    {
      "keyword": "dog foods",
      "volume": 2281
    },
    {
      "keyword": "hills pet food",
      "volume": 2200
    },
    {
      "keyword": "vet recommended dog food",
      "volume": 1927
    },
    {
      "keyword": "prescription dog food",
      "volume": 1639
    },
    {
      "keyword": "american journey dog food",
      "volume": 1502
    },
    {
      "keyword": "dog food brands to avoid",
      "volume": 1262
    },
    {
      "keyword": "best dry dog food brands",
      "volume": 1245
    },
    {
      "keyword": "low cost vaccines",
      "volume": 1237
    },
    {
      "keyword": "purina pro plan urinary",
      "volume": 1075
    },
    {
      "keyword": "hills prescription dog food",
      "volume": 1074
    },
    {
      "keyword": "vet recommended puppy food",
      "volume": 941
    },
    {
      "keyword": "royal canin veterinary diet",
      "volume": 928
    },
    {
      "keyword": "purina veterinary diets",
      "volume": 863
    },
    {
      "keyword": "prescription cat food",
      "volume": 850
    },
    {
      "keyword": "hills prescription diet",
      "volume": 800
    },
    {
      "keyword": "hills diet dog food",
      "volume": 800
    },
    {
      "keyword": "royal canin urinary dog food",
      "volume": 531
    },
    {
      "keyword": "hills vet",
      "volume": 510
    },
    {
      "keyword": "vet dog food",
      "volume": 504
    },
    {
      "keyword": "top 10 dog foods",
      "volume": 500
    },
    {
      "keyword": "food dog",
      "volume": 483
    },
    {
      "keyword": "dog food recipes vet approved",
      "volume": 430
    },
    {
      "keyword": "hillsvet",
      "volume": 414
    },
    {
      "keyword": "prescription diet dog food",
      "volume": 395
    },
    {
      "keyword": "purina dm dog food",
      "volume": 374
    },
    {
      "keyword": "purina diabetic dog food",
      "volume": 370
    },
    {
      "keyword": "free pet exam near me",
      "volume": 344
    },
    {
      "keyword": "does petsmart have a vet",
      "volume": 322
    },
    {
      "keyword": "hills veterinary diet",
      "volume": 303
    },
    {
      "keyword": "hills prescription diet cat food",
      "volume": 298
    },
    {
      "keyword": "best dog food brands for small dogs",
      "volume": 272
    },
    {
      "keyword": "veterinary prescription",
      "volume": 255
    },
    {
      "keyword": "therapeutic dog",
      "volume": 252
    },
    {
      "keyword": "dog food recipes homemade",
      "volume": 252
    },
    {
      "keyword": "therapeutic dogs",
      "volume": 240
    },
    {
      "keyword": "hills id dry dog food",
      "volume": 207
    },
    {
      "keyword": "hills prescription diet coupons",
      "volume": 198
    },
    {
      "keyword": "hills id canned dog food",
      "volume": 196
    },
    {
      "keyword": "prescription dog food for allergies",
      "volume": 195
    },
    {
      "keyword": "prescription pet food",
      "volume": 193
    },
    {
      "keyword": "hill's pet food",
      "volume": 174
    },
    {
      "keyword": "petco prescription dog food",
      "volume": 163
    },
    {
      "keyword": "petco hill's prescription diet",
      "volume": 163
    },
    {
      "keyword": "hills dog food petsmart",
      "volume": 158
    },
    {
      "keyword": "petco brands",
      "volume": 128
    },
    {
      "keyword": "purina pro plan veterinary diet",
      "volume": 125
    },
    {
      "keyword": "royal canin veterinary diet gastrointestinal",
      "volume": 118
    },
    {
      "keyword": "what dog food do vets recommend",
      "volume": 111
    },
    {
      "keyword": "prescription diets for dogs",
      "volume": 106
    },
    {
      "keyword": "vet recommended dog food brands",
      "volume": 105
    },
    {
      "keyword": "purina pro plan veterinary diets ha",
      "volume": 100
    },
    {
      "keyword": "vet cat food",
      "volume": 97
    },
    {
      "keyword": "hills sd",
      "volume": 96
    },
    {
      "keyword": "prescription dog food brands",
      "volume": 93
    },
    {
      "keyword": "hill's prescription dog food",
      "volume": 92
    },
    {
      "keyword": "hill's prescription",
      "volume": 92
    },
    {
      "keyword": "pet vet pet food",
      "volume": 90
    },
    {
      "keyword": "royal canin veterinary diet urinary so",
      "volume": 77
    },
    {
      "keyword": "royal canin veterinary",
      "volume": 76
    },
    {
      "keyword": "royal canin prescription diet",
      "volume": 72
    },
    {
      "keyword": "veterinary diet dog food",
      "volume": 71
    },
    {
      "keyword": "highest rated dry dog food",
      "volume": 70
    },
    {
      "keyword": "royal canin veterinary diet cat",
      "volume": 69
    },
    {
      "keyword": "what wet dog food do vets recommend",
      "volume": 64
    },
    {
      "keyword": "prescription pet food regulations",
      "volume": 47
    },
    {
      "keyword": "hills prescription diet urinary care",
      "volume": 46
    },
    {
      "keyword": "do vets recommend grain-free dog food",
      "volume": 45
    },
    {
      "keyword": "veterinary diet",
      "volume": 36
    },
    {
      "keyword": "vet diets",
      "volume": 32
    },
    {
      "keyword": "hills prescription dog food where to buy",
      "volume": 31
    },
    {
      "keyword": "are dogs therapeutic",
      "volume": 27
    },
    {
      "keyword": "prescription pet food online",
      "volume": 25
    },
    {
      "keyword": "hills brothers dog food",
      "volume": 24
    },
    {
      "keyword": "waltham canine high fiber",
      "volume": 24
    },
    {
      "keyword": "dog in food",
      "volume": 24
    },
    {
      "keyword": "hill's prescription diet petco",
      "volume": 24
    },
    {
      "keyword": "purina pro plan veterinary diets for cats",
      "volume": 23
    },
    {
      "keyword": "best dog food comparison",
      "volume": 21
    },
    {
      "keyword": "banfield heartworm treatment cost",
      "volume": 19
    },
    {
      "keyword": "prescription dog food online",
      "volume": 18
    },
    {
      "keyword": "hills digestive care canned dog food",
      "volume": 16
    },
    {
      "keyword": "hills prescription diet review",
      "volume": 15
    },
    {
      "keyword": "is prescription cat food necessary",
      "volume": 15
    },
    {
      "keyword": "royal canin veterinary diet dog food",
      "volume": 13
    },
    {
      "keyword": "hills nutrition dog food",
      "volume": 12
    },
    {
      "keyword": "royal canin veterinary prescription diets",
      "volume": 5
    },
    {
      "keyword": "hills prescription diet canada",
      "volume": 3
    },
    {
      "keyword": "vet recommended dog food 2018",
      "volume": 1
    },
    {
      "keyword": "royal canin prescription dog food online",
      "volume": 1
    },
    {
      "keyword": "hills prescription diet wd dry dog food",
      "volume": 0
    },
    {
      "keyword": "innovative veterinary diets for dogs",
      "volume": 0
    },
    {
      "keyword": "innovative veterinary diets dog food",
      "volume": 0
    },
    {
      "keyword": "prescription pet food lawsuit",
      "volume": null
    },
    {
      "keyword": "hill's prescription diet treats for dogs",
      "volume": null
    },
    {
      "keyword": "hill's prescription diet dog treats",
      "volume": null
    },
    {
      "keyword": "hills prescription diet treats recipe",
      "volume": null
    },
    {
      "keyword": "hills prescription diet hypo treats",
      "volume": null
    },
    {
      "keyword": "hills prescription diet soft baked treats",
      "volume": null
    },
    {
      "keyword": "petsmart prescription dog food",
      "volume": null
    },
    {
      "keyword": "hills veterinary diet dog food",
      "volume": null
    },
    {
      "keyword": "hills dog food suppliers",
      "volume": null
    },
    {
      "keyword": "i don't have any dog food",
      "volume": null
    },
    {
      "keyword": "more more dog food",
      "volume": null
    },
    {
      "keyword": "t dog foods",
      "volume": null
    },
    {
      "keyword": "prescription diet id canned dog food",
      "volume": null
    },
    {
      "keyword": "prescribed science diet canned dog food",
      "volume": null
    },
    {
      "keyword": "hills prescription canned dog food",
      "volume": null
    },
    {
      "keyword": "veterinary exclusive hill's prescription diet",
      "volume": null
    },
    {
      "keyword": "hills prescription id canned dog food",
      "volume": null
    },
    {
      "keyword": "prescribed hills diet canned dog food",
      "volume": null
    },
    {
      "keyword": "hill's prescription diet i d digestive care low fat chicken flavor dry dog food",
      "volume": null
    },
    {
      "keyword": "hill's prescription diet metabolic mobility weight and joint care chicken flavor dry dog food",
      "volume": null
    },
    {
      "keyword": "veterinary exclusive hill's prescription diet feline",
      "volume": null
    },
    {
      "keyword": "purina prescription cat food veterinary diets",
      "volume": null
    },
    {
      "keyword": "prescription diet i/d dry dog food",
      "volume": null
    },
    {
      "keyword": "hill's prescription diet z/d dry dog food",
      "volume": null
    },
    {
      "keyword": "do petsmart have vet",
      "volume": null
    },
    {
      "keyword": "petsmarts vet",
      "volume": null
    },
    {
      "keyword": "petsmart with vet",
      "volume": null
    },
    {
      "keyword": "vets petsmart",
      "volume": null
    },
    {
      "keyword": "can i buy prescription at petco",
      "volume": null
    },
    {
      "keyword": "hills prescription diet petco",
      "volume": null
    },
    {
      "keyword": "petco prescription how long to verify",
      "volume": null
    },
    {
      "keyword": "veterinary advice dog diet",
      "volume": null
    },
    {
      "keyword": "blue natural veterinary diet dog food",
      "volume": null
    },
    {
      "keyword": "raw food diet dogs veterinarian",
      "volume": null
    },
    {
      "keyword": "dogs are therapeutic",
      "volume": null
    },
    {
      "keyword": "how are dogs therapeutic",
      "volume": null
    },
    {
      "keyword": "petsmart prescription food",
      "volume": null
    },
    {
      "keyword": "purina pro plan hypoallergenic",
      "volume": null
    },
    {
      "keyword": "royal canin veterinary diet dog",
      "volume": null
    },
    {
      "keyword": "hills prescription pet food",
      "volume": null
    },
    {
      "keyword": "veterinary dog food",
      "volume": null
    }
  

]
"""
pim_attribute_filters = """
🔍 Attribute Filters (Apply only if matching):
[{
  "attributeDetails": [
    {
      "type": "TEXT",
      "name": "Sku",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "EAN",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Tagline",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "UPC",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Headline",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 1 Heading",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 1 Info",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product SKU 1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 1 SKU 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 2 Heading",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 2 Info",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 2 SKU 1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 2 SKU 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 3 Heading",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 3 Info",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 3 SKU 1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 3 SKU 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 4 Heading",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 4 Info",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 4 SKU 1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Product 4 SKU 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Alternate Sub Headline",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "App Reminder",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Autoship Option",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Auxiliary Media",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Backorder Message",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Backorder Date",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Banned Products",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Bought Together Products",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Brand",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Bazaarvoice Average Rating",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Bazaarvoice Rating Range",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Bazaarvoice Review Count",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Confirmation Banner Asset ID",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Controlled States",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Manufacturer",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Disallow as recommendation",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Discontinued Upsell 1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Discontinued Upsell 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Discontinued Upsell 3",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Do not ship to countries",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Expert Rating",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Fixed Related Products",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Flavor",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Frequently Bought Together Override",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Generic Alternative Title",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Generic Equivalent Product ID",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Generic Name",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Generics Page Text",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Group",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "How to use tab rwd",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ingredients Filter",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ingredients",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ingredients Tab Name",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ingredients Tab Rwd",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "JW Player Video ID",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "JW Player Video ID 2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "JW Player Video ID 3",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "JW Player Video ID 4",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "JW Player Video ID 5",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Label Postfix",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Life",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Localized Tax Class",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Product Details",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Manufacturer Product ID",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Max Weight",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Milligrams",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Minimum Weight",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "MPN",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Name",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "No Tax in States",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Page Description",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Page Keywords",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Page Title",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Page URL",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "PCRX Part Number",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "PDP Shipping Message",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Pill Count",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Pill Count Variation",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "PageKeywords",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Private Label",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Product Info Tab Rwd",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Promo Icon Image",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Promo Icon Start Date",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Promo Icon Position",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Recommended Skus",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Restricted Shipping States",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "RWD Alternate",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "RWD Generic",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Searchable If Unavailable",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ship Height",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ship Length",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Shipping Notification",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ship Weight",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Ship Width",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Description",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "SKU Color",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Sku Flavor",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "SKU Size",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "SKU Weight",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Sort Attribute",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Species",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Store Tax Class",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Supplier",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Supplier Product ID",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Tax Class",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Rendering Template",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Title Tag",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Unit",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Unit Measure",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Upsell",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Usage Image URL",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Weight Shared Variation",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Workato XML Export Date",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "parentSku",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "preview_url",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Main Image",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Active",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Default SKU",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Brief Selling Point",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Classification Category",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Primary Category",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Variation Attribute",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Acute/Chronic",
      "validation": {
        "acceptedValues": [
          "acute",
          "chronic",
          "chronic-acute"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Administration Form",
      "validation": {
        "acceptedValues": [
          "injection",
          "irrigation",
          "oral",
          "otic"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Age Range",
      "validation": {
        "acceptedValues": [
          "0 - 2 Years",
          "3 - 5 Years",
          "6 - 8 Years",
          "9 - 12 Years"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Age Requirement",
      "validation": {
        "acceptedValues": [
          "newborn+",
          "8-weeks+",
          "12-weeks+",
          "6-months+"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "ApplyTo",
      "validation": {
        "acceptedValues": [
          "face",
          "mouth",
          "eyes",
          "ears"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Bandage Width x Length",
      "validation": {
        "acceptedValues": [
          "2-in-W-5-yd-L",
          "3-in-W-5-yd-L",
          "4-in-W-5-yd-L",
          "n-a"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #1",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #2",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #3",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #4",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #5",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #6",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Benefits: Bullet Point #7",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Catalog",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Category Codes (Rx)",
      "validation": {
        "acceptedValues": [
          "spot-treatment",
          "oral-treatment",
          "flea-tick-shampoo",
          "powder-spray-wipe"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Cautions",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Collar Type (OTC)",
      "validation": {
        "acceptedValues": [
          "herbal",
          "flea-and-tick"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Compound Flavor",
      "validation": {
        "acceptedValues": [
          "cherry",
          "chicken",
          "beef",
          "tuna"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Dental Features",
      "validation": {
        "acceptedValues": [
          "breath-freshening",
          "plaque-removal",
          "tartar-removal",
          "yeast-removal"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Directions",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Dosage for Cats",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Dosage for Dogs",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Dosage for Horses",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Drug Type",
      "validation": {
        "acceptedValues": [
          "adrenergic-agent",
          "alkalizer",
          "analgesic",
          "antiviral"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "FoodFormDetail",
      "validation": {
        "acceptedValues": [
          "air-dried",
          "birdseed",
          "bloodworms",
          "cracked-corn"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "FoodTexture",
      "validation": {
        "acceptedValues": [
          "aspic-gelee",
          "bits-in-broth",
          "chunks-in-gravy",
          "flaked"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "FoodTopCategory",
      "validation": {
        "acceptedValues": [
          "Food",
          "Treats",
          "Prescription_Food_Treats"
        ]
      }
    },
    {
      "type": "BOOLEAN",
      "name": "Frozen",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Gender Suitable",
      "validation": {
        "acceptedValues": [
          "female",
          "male",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Horse Size",
      "validation": {
        "acceptedValues": [
          "draft-horse"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "How does this product work?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "How should <Product Name> be given/administered?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "How should I store this product:",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Legal Qs (Prop65)",
      "validation": {
        "acceptedValues": [
          "No",
          "Yes - Cancer",
          "Yes - Reproductive Harm",
          "Yes - Cancer and Reproductive Harm"
        ]
      }
    },
    {
      "type": "BOOLEAN",
      "name": "Marketing Exclusion",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Materials/Ingredients table",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "National Drug Code",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Subbrand",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Pill/Each",
      "validation": {
        "acceptedValues": [
          "pill",
          "each"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Prescription_Food_Treats",
      "validation": {
        "acceptedValues": [
          "Prescription Dry Food",
          "Prescription Wet Food",
          "Prescription Biscuits & Crunchy Treats",
          "Prescription Soft & Chewy Treats"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Product Class-L4",
      "validation": {
        "acceptedValues": [
          "rx-none",
          "rx-flea-tick-heartworm",
          "rx-flea-tick",
          "rx-heartworm"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Product Class-Rx",
      "validation": {
        "acceptedValues": [
          "rx-other",
          "rx-nsaid",
          "rx-heart",
          "rx-derm",
          "rx-motion-sickness",
          "rx-infection",
          "rx-insulin",
          "rx-incontinence",
          "rx-cushings-disease",
          "rx-virus",
          "rx-medical-equipment",
          "rx-anxiety",
          "rx-behavior-issues",
          "rx-cancer",
          "rx-parasiticide",
          "rx-allergies",
          "rx-brain",
          "rx-digestive",
          "rx-eye-ear",
          "rx-recovery-dehydration",
          "rx-thyroid",
          "rx-vitamins"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Product Details (What is <Product Name>?)",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Product Weight",
      "validation": {
        "acceptedValues": [
          "Less-than-5",
          "5-10-lbs",
          "11-20-lbs",
          "21-30-lbs"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "ProductForm",
      "validation": {
        "acceptedValues": [
          "block",
          "capsule",
          "chew",
          "chewable-tablet",
          "crumble",
          "cleanser",
          "diffuser",
          "gel",
          "granules",
          "injectable",
          "liquid",
          "lotion",
          "ointment",
          "packing",
          "paste",
          "pellet",
          "pill",
          "powder",
          "puree",
          "rinse",
          "soft-chew",
          "softgel",
          "solution",
          "spray",
          "suspension",
          "tablet",
          "wafer",
          "wipes",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "ProductWeightOunces",
      "validation": {
        "acceptedValues": [
          "less-than-3",
          "3-5-oz",
          "6-9-oz",
          "10-12-oz",
          "12-oz-and-above"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Prop65 Attachment Verbiage",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "PurchaseUOM",
      "validation": {
        "acceptedValues": [
          "Each",
          "Case",
          "Master Case",
          "pack"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Pyrethrins",
      "validation": {
        "acceptedValues": [
          "Yes"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Pyrethrins Caution Statement",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Restricted Availability",
      "validation": {
        "acceptedValues": [
          "TRUE"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Rx & OTC Category",
      "validation": {
        "acceptedValues": [
          "Flea, Tick, Mite & Dewormers: Spot Treatments",
          "Flea, Tick, Mite & Dewormers: Oral Treatments",
          "Flea, Tick, Mite & Dewormers: Shampoos & Dips",
          "Flea, Tick, Mite & Dewormers: Powders, Sprays & Wipes"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Season (OTC)",
      "validation": {
        "acceptedValues": [
          "year-round",
          "spring-summer",
          "fall-winter"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Suitable For:",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Unit Type",
      "validation": {
        "acceptedValues": [
          "mL",
          "tablets"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "z-human-label",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What are the potential side effects of <Product Name>?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What happens if I miss giving a dose of <Product Name>?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What happens if I overdose my pet on <Product Name>?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What is the most important thing I should know about <Product Name>",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What other drugs will affect <Product Name>?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What should I avoid while giving <Product Name> to my pet?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What should I discuss with my veterinarian before giving <Product Name> to my pet?",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "What special precautions are there?",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Bird Type",
      "validation": {
        "acceptedValues": [
          "african-grey",
          "amazon",
          "budgie",
          "canary",
          "chicken",
          "cockatiel",
          "cockatoo",
          "conure",
          "dove-pigeon",
          "duck",
          "finch",
          "hookbill",
          "lovebird",
          "macaw",
          "parakeet",
          "parrot",
          "quaker",
          "wild-bird",
          "parrotlet",
          "bluebird",
          "bunting",
          "cardinal",
          "catbird",
          "chickadee",
          "chukar",
          "dove",
          "emu",
          "flicker",
          "gamebird",
          "geese",
          "grosbeak",
          "grouse",
          "guinea-fowl",
          "hummingbird",
          "jay",
          "junco",
          "kinglet",
          "kiwi",
          "mockingbird",
          "nuthatch",
          "oriole",
          "ostrich",
          "patridge",
          "pheasant",
          "poultry",
          "quail",
          "redpoll",
          "robin",
          "senegal",
          "siskin",
          "songbird",
          "sparrow",
          "squab",
          "starling",
          "swan",
          "tanager",
          "thrasher",
          "thrush",
          "titmouse",
          "towhee",
          "turkey",
          "vireo",
          "warbler",
          "waterfowl",
          "woodpecker",
          "wren",
          "toucan",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Chew Style",
      "validation": {
        "acceptedValues": [
          "moderate",
          "extreme",
          "tough",
          "Light"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Closure Type Diapers",
      "validation": {
        "acceptedValues": [
          "adhesive",
          "hook-and-loop",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Health Feature",
      "validation": {
        "acceptedValues": [
          "allergy-relief",
          "anti-fungal-anti-bacterial",
          "anti-parasitic",
          "appetite-stimulation",
          "brain-health",
          "calming",
          "circulatory-care",
          "colic-relief",
          "dander",
          "dental-and-breath-care",
          "diabetic-support",
          "digestive-health",
          "ear-care",
          "ear-mite-treatment",
          "eye-care",
          "first-aid",
          "hairball-control",
          "heart-care",
          "high-energy",
          "high-protein",
          "hip-and-joint-support",
          "hoof-care",
          "hormone-support",
          "immune-support",
          "itch-and-redness-remedy",
          "kidney-care",
          "liver-care",
          "milk-replacer",
          "muscle-care",
          "nerve-care",
          "paw-care",
          "pest-control",
          "recovery",
          "reproduction-and-nursing",
          "respiratory-care",
          "senior-care",
          "sensitive-digestion",
          "sensitive-skin",
          "shedding-control",
          "skin-and-coat-health",
          "sun-protection",
          "tear-stain-removal",
          "thyroid-support",
          "urinary-tract-health",
          "vitamins-and-minerals",
          "weight-management",
          "n-a",
          "metabolic",
          "electrolyte"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Intro Year",
      "validation": {
        "acceptedValues": [
          "2014",
          "2015",
          "2016",
          "2017",
          "2018",
          "2019",
          "2020",
          "2021",
          "2022",
          "2023",
          "2024",
          "2025",
          "2026",
          "2027"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Kill/Treats",
      "validation": {
        "acceptedValues": [
          "biting-flies",
          "chewing-lice",
          "ear-mites",
          "flea-eggs",
          "flea-larvae",
          "fleas",
          "heartworms",
          "hookworms",
          "mosquitoes",
          "roundworms",
          "tapeworms",
          "ticks",
          "whipworms",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Made In",
      "validation": {
        "acceptedValues": [
          "afghanistan",
          "albania",
          "algeria",
          "andorra",
          "angola",
          "antigua-and-barbuda",
          "argentina",
          "armenia",
          "australia",
          "austria",
          "azerbaijan",
          "the-bahamas",
          "bahrain",
          "bangladesh",
          "barbados",
          "belarus",
          "belgium",
          "belize",
          "benin",
          "bhutan",
          "bolivia",
          "bosnia-and-herzegovina",
          "botswana",
          "brazil",
          "brunei",
          "bulgaria",
          "burkina-faso",
          "burundi",
          "cabo-verde",
          "cambodia",
          "cameroon",
          "canada",
          "central-african-republic",
          "chad",
          "chile",
          "china",
          "colombia",
          "comoros",
          "democratic-republic-of-the-congo",
          "republic-of-the-congo",
          "costa-rica",
          "cÃ´te-dâ€™ivoire",
          "croatia",
          "cuba",
          "cyprus",
          "czech-republic",
          "denmark",
          "djibouti",
          "dominica",
          "dominican-republic",
          "east-timor-(timor-leste)",
          "ecuador",
          "egypt",
          "el-salvador",
          "equatorial-guinea",
          "eritrea",
          "estonia",
          "eswatini",
          "ethiopia",
          "fiji",
          "finland",
          "france",
          "gabon",
          "the-gambia",
          "georgia",
          "germany",
          "ghana",
          "greece"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Materials/Ingredients text",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "One Time Buy",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Recovery Product Type",
      "validation": {
        "acceptedValues": [
          "elizabethan-collar-e-collar",
          "soft-cone",
          "inflatable-cone",
          "suit",
          "boot"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Repels/Prevents",
      "validation": {
        "acceptedValues": [
          "biting-flies",
          "chewing-lice",
          "ear-mites",
          "flea-eggs",
          "flea-larvae",
          "fleas",
          "heartworms",
          "hookworms",
          "mosquitoes",
          "roundworms",
          "tapeworms",
          "ticks",
          "whipworms",
          "n-a"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Sellable UOM",
      "validation": {
        "acceptedValues": [
          "each",
          "pill",
          "bottle",
          "case",
          "pack"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Sourced From",
      "validation": {
        "acceptedValues": [
          "afghanistan",
          "albania",
          "algeria",
          "andorra",
          "angola",
          "antigua-and-barbuda",
          "argentina",
          "armenia",
          "australia",
          "austria",
          "azerbaijan",
          "the-bahamas",
          "bahrain",
          "bangladesh",
          "barbados",
          "belarus",
          "belgium",
          "belize",
          "benin",
          "bhutan",
          "bolivia",
          "bosnia-and-herzegovina",
          "botswana",
          "brazil",
          "brunei",
          "bulgaria",
          "burkina-faso",
          "burundi",
          "cabo-verde",
          "cambodia",
          "cameroon",
          "canada",
          "central-african-republic",
          "chad",
          "chile",
          "china",
          "colombia",
          "comoros",
          "democratic-republic-of-the-congo",
          "republic-of-the-congo",
          "costa-rica",
          "cÃ´te-dâ€™ivoire",
          "croatia",
          "cuba",
          "cyprus",
          "czech-republic",
          "denmark",
          "djibouti",
          "dominica",
          "dominican-republic",
          "east-timor-(timor-leste)",
          "ecuador",
          "egypt",
          "el-salvador",
          "equatorial-guinea",
          "eritrea",
          "estonia",
          "eswatini",
          "ethiopia",
          "fiji",
          "finland",
          "france",
          "gabon",
          "the-gambia",
          "georgia",
          "germany",
          "ghana",
          "greece"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Test Type",
      "validation": {
        "acceptedValues": [
          "allergy-testing",
          "blood-glucose-monitoring",
          "dna-testing",
          "food-intolerance-testing",
          "leukemia-testing",
          "urine-testing",
          "worm-testing",
          "n-a"
        ]
      }
    },
    {
      "type": "NUMBER",
      "name": "Autoship Discount Percent",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Bazaarvoice Filter Rating",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Below the Fold",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Change Frequency",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Controlled Substance?",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Disable Order Autoship",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Discountable",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Do not split",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Drop Ship",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Expert Review Count",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Food Form",
      "validation": {
        "acceptedValues": [
          "Dry Food",
          "Wet Food",
          "Freeze Dried",
          "Dehydrated",
          "Frozen",
          "Dry Food Toppings",
          "Wet Food Toppings",
          "Other",
          "Health Specific Diets",
          "Vet Diet",
          "OTC"
        ]
      }
    },
    {
      "type": "BOOLEAN",
      "name": "Ground Only",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Has Map Price?",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Has Medication Reminder Option",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Health Condition Therapy",
      "validation": {
        "acceptedValues": [
          "Anxiety",
          "Arthritis",
          "Asthma",
          "Bacterial",
          "Behavior",
          "Bone / Joint",
          "Cancer",
          "Cough",
          "Dental",
          "Diabetes",
          "Digestive",
          "Eye",
          "Fleas",
          "Flies",
          "Glaucoma",
          "Hairball",
          "Heart / Blood Pressure",
          "Heartworm",
          "Hoof",
          "Hormonal / Endocrine",
          "Immunity",
          "Incontinence",
          "Itch",
          "Kidney",
          "Liver",
          "Mites",
          "Nail",
          "Nausea",
          "Odor",
          "Pain / Inflammation",
          "Respiratory",
          "Ringworm",
          "Seizure / Epilepsy",
          "Shedding",
          "Skin / Coat",
          "Thrush",
          "Thyroid",
          "Ticks",
          "Urinary",
          "Viral",
          "Weight",
          "Worms",
          "Wound",
          "Behavior Management",
          "Daily Maintenance",
          "Weight Maintenance",
          "Dental Care",
          "Dental & Breath Care",
          "Digestive Care",
          "Glucose Management",
          "Urinary Care",
          "Eye Health",
          "Skin Care",
          "Heart Health",
          "Kidney Care",
          "Liver Function",
          "Heartworm Prevention",
          "Joint Care",
          "Weight Management",
          "Liver Support",
          "Nutrition",
          "Senior Health",
          "Thyroid Support",
          "Hip/Joint",
          "Allergies",
          "Fungal Infections",
          "Ear Care",
          "Joint Pain",
          "Behavior Issues",
          "Constipation",
          "Cushings Disease",
          "Dehydration",
          "Digestive Issues",
          "Ear Infections",
          "Ear Mites",
          "Hair Loss",
          "High Blood Pressure",
          "Congestive Heart Failure",
          "Heartburn",
          "Hookworms",
          "Hypothyroidism",
          "Hyperthyroidism",
          "Hot Spots",
          "Inflammatory Bowel Disease",
          "Kennel Cough",
          "Kidney Disease",
          "Kidney Stones",
          "Liver Disease",
          "Mange",
          "Motion Sickness",
          "Muscle Spasms / Tremors",
          "N/A",
          "Unhealthy Weight",
          "Warts",
          "Vomiting",
          "Urinary Tract Infections",
          "Ulcers",
          "Tapeworms",
          "Skin Infections",
          "Senility",
          "Roundworms",
          "Respiratory Infections",
          "Recovery",
          "Parvovirus",
          "Eye Infections",
          "Allergy"
        ]
      }
    },
    {
      "type": "BOOLEAN",
      "name": "Hide Generic / Alternative Area",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Ice Pack",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "In Stock",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Life Stage",
      "validation": {
        "acceptedValues": [
          "all-lifestages",
          "adult",
          "baby",
          "nursing",
          "senior",
          "kitten",
          "puppy",
          "All Life Stages",
          "Large Breed",
          "Small Breed"
        ]
      }
    },
    {
      "type": "NUMBER",
      "name": "List Price",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "MAP Price",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Max Autoship Discount Amount",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Max Quantity",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Minimum Order Quantity",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Online",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Package Quantity",
      "validation": {
        "acceptedValues": [
          "1",
          "2",
          "3",
          "4",
          "6",
          "7",
          "12",
          "14",
          "15",
          "18",
          "24",
          "28",
          "30",
          "42",
          "60",
          "90",
          "100",
          "120",
          "180",
          "540",
          "Less-than-5",
          "5-10-count",
          "11-20-count",
          "21-30-count",
          "31-40-count",
          "41-and-above"
        ]
      }
    },
    {
      "type": "NUMBER",
      "name": "Promo Icon Expiry",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "RWD Alternate Equivalent",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "RWD Generic Equivalent",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Sales Volume",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Search Placement",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Search Rank",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Searchable",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Show Bundled Items",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Show Mfr Message",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Show Multi Pre Cart",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Show Real Price?",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Step Quantity",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Vet Authorized Product",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Sale Price",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Map Priority",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Pet Size",
      "validation": {
        "acceptedValues": [
          "allbreeds",
          "x-small",
          "small",
          "medium",
          "large",
          "giant",
          "X-Large",
          "All Breed Sizes"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Online From",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Online To",
      "validation": {}
    },
    {
      "type": "NUMBER",
      "name": "Unit Quantity",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "z-generic",
      "validation": {
        "acceptedValues": [
          "TRUE"
        ]
      }
    },
    {
      "type": "OPTIONS",
      "name": "Package Type",
      "validation": {
        "acceptedValues": [
          "Dry",
          "Wet",
          "N/A",
          "Freeze Dried",
          "Box"
        ]
      }
    },
    {
      "type": "TEXT",
      "name": "Generic",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Pillform",
      "validation": {
        "acceptedValues": [
          "Liquid",
          "Tablet",
          "Caplet",
          "Capsule",
          "Chewable Tablet",
          "Soft Chew",
          "Injectable",
          "Powder",
          "Suspension",
          "Solution",
          "Transdermal"
        ]
      }
    },
    {
      "type": "NUMBER",
      "name": "Product Key Old",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Selling channel",
      "validation": {
        "acceptedValues": [
          "petmeds",
          "pcrx",
          "care"
        ]
      }
    },
    {
      "type": "NUMBER",
      "name": "Expiry Date",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "New Arrival Flag",
      "validation": {}
    },
    {
      "type": "BOOLEAN",
      "name": "Discontinued Flag",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Syringe Size",
      "validation": {
        "acceptedValues": [
          "U-40",
          "U-60",
          "U-80"
        ]
      }
    },
    {
      "type": "BOOLEAN",
      "name": "Transition Out Flag",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Product Key",
      "validation": {}
    },
    {
      "type": "TEXT",
      "name": "Product Short Name",
      "validation": {}
    },
    {
      "type": "OPTIONS",
      "name": "Supplies",
      "validation": {
        "acceptedValues": [
          "Toys",
          "Beauty & Grooming",
          "Waste Management",
          "Cleaners & Stain Removers",
          "Bowls & Feeding",
          "Training & Behavior",
          "Beds, Crates, & Gear",
          "Test Kits",
          "Litter & Accessories",
          "Cat Furniture"
        ]
      }
    }
  ]
}]
"""
frequently_asked_questions = """
{
  "questions": [
    {
      "topic": "How This Product Works",
      "topic_questions": [
        "How does this treatment kill fleas and ticks?",
        "How fast does this product start working?",
        "What parasites does this product protect against?",
        "Does this product prevent future flea infestations?"
      ]
    },
    {
      "topic": "Application & Usage",
      "topic_questions": [
        "How do I apply this product to my dog?",
        "How often should I use this flea and tick treatment?",
        "Can I use this product year-round?",
        "Is it safe to bathe my dog after using this product?"
      ]
    },
    {
      "topic": "Safety & Side Effects",
      "topic_questions": [
        "Is this product safe for puppies?",
        "What are the possible side effects of this flea and tick treatment?",
        "Can this product be used on pregnant or nursing dogs?",
        "What should I do if my dog has a reaction to this treatment?"
      ]
    },
    {
      "topic": "Dosing & Sizing",
      "topic_questions": [
        "What size dog is this product suitable for?",
        "Can I use this treatment on dogs under [X] lbs?",
        "How do I choose the right dose or size for my dog?",
        "Can I split this treatment between multiple dogs?"
      ]
    },
    {
      "topic": "Compatibility & Product Comparisons",
      "topic_questions": [
        "Can this product be used with other flea and tick treatments?",
        "Is this product compatible with heartworm medication?",
        "How does this treatment compare to other flea and tick products?"
      ]
    },
    {
      "topic": "Effectiveness & Reapplication",
      "topic_questions": [
        "How long does this product protect my dog?",
        "What should I do if I miss a dose?",
        "Can I reapply this treatment early if I still see fleas?",
        "Does this product kill flea eggs and larvae too?"
      ]
    },
    {
      "topic": "Prescription & Purchasing Info",
      "topic_questions": [
        "Do I need a prescription to buy this flea and tick treatment?",
        "Is this product available over the counter?",
        "Can I buy this product online?",
        "Can I use an old prescription to reorder this product?"
      ]
    }
  ]
}
"""
